import { PrismaClient } from "@prisma/client"

declare global {
    var prisma: PrismaClient | undefined
}

// Verificar si estamos en el Edge Runtime
const isEdgeRuntime = typeof globalThis === 'undefined'

// Solo usar globalThis si no estamos en Edge Runtime
const globalForPrisma = isEdgeRuntime ? { prisma: undefined } : globalThis as unknown as {
    prisma: PrismaClient | undefined
}

export const db = globalForPrisma.prisma ?? new PrismaClient()

if (process.env.NODE_ENV !== "production" && !isEdgeRuntime) {
    globalForPrisma.prisma = db
}

export default db