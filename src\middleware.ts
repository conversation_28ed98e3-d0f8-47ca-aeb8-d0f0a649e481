import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export function middleware(request: NextRequest) {
  // Interceptar errores de Better Auth y redirigir a página personalizada
  console.log('Intercepted auth error:', request.nextUrl.searchParams.get('error'));
  console.log('Request URL:', request.url);
  if (request.nextUrl.pathname === '/api/auth/error') {
    const error = request.nextUrl.searchParams.get('error')
    const redirectUrl = new URL('/auth-error', request.url)

    if (error) {
      redirectUrl.searchParams.set('error', error)
    }

    return NextResponse.redirect(redirectUrl)
  }

  // Allow public routes
  const publicRoutes = [
    '/api/auth',
    '/access-denied',
    '/unauthorized',
    '/auth-error',
    '/login',
    '/_next',
    '/favicon.ico'
  ]

  // Check if the current path is a public route
  const isPublicRoute = publicRoutes.some(route =>
    request.nextUrl.pathname.startsWith(route)
  )

  if (isPublicRoute) {
    return NextResponse.next()
  }

  // For now, allow all other routes
  // The actual authentication check will be done in the layout components
  // using getServerSession and our auth-guard
  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes) - EXCEPT /api/auth/error which we want to intercept
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
    '/api/auth/error' // Specifically include this API route for interception
  ],
}
