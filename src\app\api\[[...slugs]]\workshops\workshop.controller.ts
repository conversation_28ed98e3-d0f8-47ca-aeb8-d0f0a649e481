import { Elysia, t } from 'elysia'
import { PrismaClient, UserRole } from '@prisma/client'
import { getServerSession } from '@/lib/getSession'
import { authMiddleware } from '../auth/auth.middleware'
import { HttpException } from '@/lib/exceptions/HttpExceptions'
import { checkAdminMiddleware } from '../middlewares/checkAdmin'

const prisma = new PrismaClient()

export const workshopController = new Elysia({ prefix: '/workshops' })
  // Get user's workshops
  .use(authMiddleware)
  .get('/user', async ({ user }) => {

      const userWorkshops = await prisma.userWorkshop.findMany({
        where: {
          userId: user.id,
          isActive: true
        },
        include: {
          workshop: true
        },
        orderBy: {
          assignedAt: 'asc'
        }
      })

      const workshops = userWorkshops.map(uw => ({
        id: uw.workshop.id,
        name: uw.workshop.name,
        location: uw.workshop.location,
        isActive: uw.workshop.isActive,
        role: uw.role
      }))

      return {
        success: true,
        data: workshops
      }
  }, {
    query: t.Optional(t.Object({
      search: t.Optional(t.String())
    }))
  })

  // Switch workshop
  .post('/switch', async ({ body, user }) => {
   

      const { workshopId } = body as { workshopId: string | null }

      // If workshopId is null, set global view (only for admins)
      if (workshopId === null) {
        if (user.role !== UserRole.ADMIN) {
          // return {
          //   success: false,
          //   error: 'Only administrators can access global view'
          // }
          throw HttpException.Forbidden('Only administrators can access global view')
        }

        // Update user's current workshop to null (global view)
        await prisma.user.update({
          where: { id: user.id },
          data: { currentWorkshopId: null }
        })

        return {
          success: true,
          data: {
            workshop: null,
            globalView: true
          }
        }
      }

      // Verify user has access to this workshop
      const userWorkshop = await prisma.userWorkshop.findUnique({
        where: {
          userId_workshopId: {
            userId: user.id,
            workshopId: workshopId
          }
        },
        include: {
          workshop: true
        }
      })

      if (!userWorkshop || !userWorkshop.isActive) {
        // return {
        //   success: false,
        //   error: 'You do not have access to this workshop'
        // }
        throw HttpException.Forbidden('You do not have access to this workshop')
      }

      // Update user's current workshop
      await prisma.user.update({
        where: { id: user.id },
        data: { currentWorkshopId: workshopId }
      })

      return {
        success: true,
        data: {
          workshop: {
            id: userWorkshop.workshop.id,
            name: userWorkshop.workshop.name,
            location: userWorkshop.workshop.location
          }
        }
      }
  }, {
    body: t.Object({
      workshopId: t.Union([t.String(), t.Null()])
    })
  })

  // Get all workshops (admin only)
  .use(checkAdminMiddleware)
  .get('/', async () => {
      // if (user.role !== UserRole.ADMIN) {
      //   throw HttpException.Forbidden('Admin access required')
      // } 

      const workshops = await prisma.workshop.findMany({
        include: {
          users: {
            where: { isActive: true }
          },
          _count: {
            select: {
              users: {
                where: { isActive: true }
              },
              clients: true,
              vehicles: true,
              receptions: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      })

      const workshopsWithStats = workshops.map(workshop => ({
        id: workshop.id,
        name: workshop.name,
        location: workshop.location,
        phone: workshop.phone,
        email: workshop.email,
        isActive: workshop.isActive,
        createdAt: workshop.createdAt,
        userCount: workshop._count.users,
        clientCount: workshop._count.clients,
        vehicleCount: workshop._count.vehicles,
        receptionCount: workshop._count.receptions
      }))

      return {
        success: true,
        data: workshopsWithStats
      }
  })

  // Create workshop (admin only)
  .post('/', async ({ body }) => {

      const { name, location, phone, email, isActive } = body as {
        name: string
        location: string
        phone?: string
        email?: string
        isActive?: boolean
      }

      if (!name || !location) {
        throw HttpException.BadRequest('Name and location are required')
      }

      // Create the workshop
      const workshop = await prisma.workshop.create({
        data: {
          name,
          location,
          phone: phone || null,
          email: email || null,
          isActive: isActive ?? true,
          settings: {
            businessHours: {
              monday: { open: '08:00', close: '18:00' },
              tuesday: { open: '08:00', close: '18:00' },
              wednesday: { open: '08:00', close: '18:00' },
              thursday: { open: '08:00', close: '18:00' },
              friday: { open: '08:00', close: '18:00' },
              saturday: { open: '08:00', close: '14:00' },
              sunday: { closed: true }
            },
            currency: 'MXN',
            timezone: 'America/Mexico_City',
            features: {
              photoEvidence: true,
              diagnostics: true,
              inventory: true,
              billing: true
            }
          }
        }
      })

      // Get all admin users to assign them to the new workshop
      const adminUsers = await prisma.user.findMany({
        where: {
          role: UserRole.ADMIN,
          isActive: true
        }
      })

      // Create UserWorkshop relationships for all admins
      const userWorkshopPromises = adminUsers.map(admin => 
        prisma.userWorkshop.create({
          data: {
            userId: admin.id,
            workshopId: workshop.id,
            role: UserRole.ADMIN,
            isActive: true,
            assignedAt: new Date()
          }
        })
      )

      await Promise.all(userWorkshopPromises)

      // If any admin doesn't have a current workshop, set this as their current
      const adminsWithoutCurrentWorkshop = adminUsers.filter(admin => !admin.currentWorkshopId)
      
      if (adminsWithoutCurrentWorkshop.length > 0) {
        const updatePromises = adminsWithoutCurrentWorkshop.map(admin =>
          prisma.user.update({
            where: { id: admin.id },
            data: { currentWorkshopId: workshop.id }
          })
        )
        await Promise.all(updatePromises)
      }

      return {
        success: true,
        data: {
          id: workshop.id,
          name: workshop.name,
          location: workshop.location,
          phone: workshop.phone,
          email: workshop.email,
          isActive: workshop.isActive,
          createdAt: workshop.createdAt,
          adminsAssigned: adminUsers.length
        }
      }
  }, {
    body: t.Object({
      name: t.String(),
      location: t.String(),
      phone: t.Optional(t.String()),
      email: t.Optional(t.String()),
      isActive: t.Optional(t.Boolean())
    })
  })

  // Get workshop details (admin only)
  .get('/:id', async ({ params }) => {

      const workshop = await prisma.workshop.findUnique({
        where: { id: params.id },
        include: {
          _count: {
            select: {
              users: {
                where: { isActive: true }
              },
              clients: true,
              vehicles: true,
              receptions: true
            }
          }
        }
      })

      if (!workshop) {
        throw HttpException.NotFound('Workshop not found')
      }

      return {
        success: true,
        data: {
          id: workshop.id,
          name: workshop.name,
          location: workshop.location,
          phone: workshop.phone,
          email: workshop.email,
          isActive: workshop.isActive,
          createdAt: workshop.createdAt,
          userCount: workshop._count.users,
          clientCount: workshop._count.clients,
          vehicleCount: workshop._count.vehicles,
          receptionCount: workshop._count.receptions
        }
      }
  })

  // Get workshop users (admin only)
  .get('/:id/users', async ({ params }) => {

      // Verify workshop exists
      const workshop = await prisma.workshop.findUnique({
        where: { id: params.id }
      })

      if (!workshop) {
        return {
          success: false,
          error: 'Workshop not found'
        }
      }

      // Get workshop users
      const workshopUsers = await prisma.userWorkshop.findMany({
        where: {
          workshopId: params.id
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true,
              emailVerified: true,
              createdAt: true,
              isActive: true
            }
          }
        },
        orderBy: {
          assignedAt: 'desc'
        }
      })

      const users = workshopUsers.map(uw => ({
        id: uw.id,
        name: uw.user.name,
        email: uw.user.email,
        role: uw.role,
        isActive: uw.isActive,
        assignedAt: uw.assignedAt,
        user: {
          image: uw.user.image,
          emailVerified: uw.user.emailVerified,
          createdAt: uw.user.createdAt
        }
      }))

      return {
        success: true,
        data: users
      }
  })
