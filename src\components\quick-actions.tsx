"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  Car, 
  Users, 
  ClipboardList, 
  Wrench, 
  BarChart3, 
  Package, 
  DollarSign,
  UserCheck,
  Settings,
  Plus
} from "lucide-react"
import Link from "next/link"
import { useRole } from "@/hooks/use-role"
import { UserRole } from "@/types"

interface QuickAction {
  href: string
  label: string
  description: string
  icon: React.ComponentType<{ className?: string }>
  variant?: 'default' | 'outline' | 'secondary'
  badge?: string
  priority?: 'high' | 'medium' | 'low'
}

export function QuickActions() {
  const { currentRole } = useRole()

  const getActionsForRole = (): QuickAction[] => {
    switch (currentRole) {
      case UserRole.ADMIN:
        return [
          {
            href: '/admin/usuarios',
            label: 'Gestionar Usuarios',
            description: 'Invitar y administrar usuarios del sistema',
            icon: Users,
            variant: 'default',
            priority: 'high'
          },
          {
            href: '/reportes',
            label: 'Ver Reportes',
            description: 'Análisis completo del taller',
            icon: BarChart3,
            variant: 'outline',
            priority: 'medium'
          },
          {
            href: '/admin/configuracion',
            label: 'Configuración',
            description: 'Ajustes del sistema',
            icon: Settings,
            variant: 'outline',
            priority: 'medium'
          },
          {
            href: '/recepcion',
            label: 'Nueva Recepción',
            description: 'Recibir vehículo en taller',
            icon: ClipboardList,
            variant: 'secondary',
            priority: 'low'
          }
        ]

      case UserRole.RECEPCIONISTA:
        return [
          {
            href: '/recepcion',
            label: 'Nueva Recepción',
            description: 'Recibir vehículo en el taller',
            icon: ClipboardList,
            variant: 'default',
            priority: 'high',
            badge: 'Principal'
          },
          {
            href: '/clientes/nuevo',
            label: 'Nuevo Cliente',
            description: 'Registrar cliente individual o flotillero',
            icon: Users,
            variant: 'outline',
            priority: 'high'
          },
          {
            href: '/vehiculos/nuevo',
            label: 'Nuevo Vehículo',
            description: 'Registrar vehículo sin recibirlo',
            icon: Car,
            variant: 'outline',
            priority: 'medium'
          },
          {
            href: '/vehiculos',
            label: 'Vehículos en Taller',
            description: 'Ver estado de vehículos',
            icon: Car,
            variant: 'secondary',
            priority: 'medium'
          },
          {
            href: '/clientes',
            label: 'Gestionar Clientes',
            description: 'Ver y editar información de clientes',
            icon: Users,
            variant: 'secondary',
            priority: 'low'
          }
        ]

      case UserRole.TECNICO:
        return [
          {
            href: '/mis-trabajos',
            label: 'Mis Trabajos',
            description: 'Órdenes de trabajo asignadas',
            icon: Wrench,
            variant: 'default',
            priority: 'high',
            badge: '5 pendientes'
          },
          {
            href: '/diagnostico',
            label: 'Nuevo Diagnóstico',
            description: 'Realizar diagnóstico de vehículo',
            icon: UserCheck,
            variant: 'outline',
            priority: 'high'
          },
          {
            href: '/inventario',
            label: 'Consultar Inventario',
            description: 'Ver disponibilidad de refacciones',
            icon: Package,
            variant: 'outline',
            priority: 'medium'
          },
          {
            href: '/vehiculos',
            label: 'Vehículos Asignados',
            description: 'Ver vehículos bajo mi responsabilidad',
            icon: Car,
            variant: 'secondary',
            priority: 'medium'
          }
        ]

      case UserRole.JEFE_TALLER:
        return [
          {
            href: '/pipeline',
            label: 'Pipeline del Taller',
            description: 'Vista general de todos los trabajos',
            icon: BarChart3,
            variant: 'default',
            priority: 'high',
            badge: 'Gestión'
          },
          {
            href: '/tecnicos',
            label: 'Gestionar Técnicos',
            description: 'Asignar trabajos y supervisar',
            icon: Users,
            variant: 'outline',
            priority: 'high'
          },
          {
            href: '/reportes',
            label: 'Reportes',
            description: 'Análisis y métricas del taller',
            icon: BarChart3,
            variant: 'outline',
            priority: 'medium'
          },
          {
            href: '/recepcion',
            label: 'Nueva Recepción',
            description: 'Recibir vehículo urgente',
            icon: ClipboardList,
            variant: 'secondary',
            priority: 'medium'
          }
        ]

      case UserRole.COMPRAS:
        return [
          {
            href: '/compras/nueva-orden',
            label: 'Nueva Orden de Compra',
            description: 'Crear orden para proveedores',
            icon: Plus,
            variant: 'default',
            priority: 'high'
          },
          {
            href: '/inventario',
            label: 'Gestionar Inventario',
            description: 'Control de stock y refacciones',
            icon: Package,
            variant: 'outline',
            priority: 'high',
            badge: '12 bajos'
          },
          {
            href: '/compras/ordenes',
            label: 'Órdenes Pendientes',
            description: 'Seguimiento de compras',
            icon: ClipboardList,
            variant: 'outline',
            priority: 'medium'
          },
          {
            href: '/proveedores',
            label: 'Gestionar Proveedores',
            description: 'Administrar catálogo de proveedores',
            icon: Users,
            variant: 'secondary',
            priority: 'medium'
          }
        ]

      case UserRole.FACTURACION:
        return [
          {
            href: '/facturacion/nueva',
            label: 'Nueva Factura',
            description: 'Generar factura para cliente',
            icon: DollarSign,
            variant: 'default',
            priority: 'high'
          },
          {
            href: '/facturacion/pendientes',
            label: 'Facturas Pendientes',
            description: 'Servicios por facturar',
            icon: ClipboardList,
            variant: 'outline',
            priority: 'high',
            badge: '8 pendientes'
          },
          {
            href: '/cobranza',
            label: 'Gestión de Cobranza',
            description: 'Seguimiento de pagos',
            icon: DollarSign,
            variant: 'outline',
            priority: 'medium'
          },
          {
            href: '/clientes',
            label: 'Clientes',
            description: 'Información fiscal y de contacto',
            icon: Users,
            variant: 'secondary',
            priority: 'medium'
          }
        ]

      default:
        return []
    }
  }

  const actions = getActionsForRole()
  const priorityActions = actions.filter(action => action.priority === 'high')
  const otherActions = actions.filter(action => action.priority !== 'high')

  if (actions.length === 0) {
    return null
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Acciones Rápidas</CardTitle>
        <CardDescription>
          Acciones principales para tu rol actual
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* High Priority Actions */}
        {priorityActions.length > 0 && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-muted-foreground">Acciones Principales</h4>
            <div className="grid grid-cols-1 gap-2">
              {priorityActions.map((action) => (
                <Button
                  key={action.href}
                  asChild
                  variant={action.variant || 'default'}
                  className="h-auto p-4 justify-start"
                >
                  <Link href={action.href}>
                    <div className="flex items-center gap-3 w-full">
                      <action.icon className="h-5 w-5 flex-shrink-0" />
                      <div className="flex-1 text-left">
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{action.label}</span>
                          {action.badge && (
                            <Badge variant="secondary" className="text-xs">
                              {action.badge}
                            </Badge>
                          )}
                        </div>
                        <p className="text-xs text-muted-foreground mt-1">
                          {action.description}
                        </p>
                      </div>
                    </div>
                  </Link>
                </Button>
              ))}
            </div>
          </div>
        )}

        {/* Other Actions */}
        {otherActions.length > 0 && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-muted-foreground">Otras Acciones</h4>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
              {otherActions.map((action) => (
                <Button
                  key={action.href}
                  asChild
                  variant={action.variant || 'outline'}
                  size="sm"
                  className="h-auto p-3 justify-start"
                >
                  <Link href={action.href}>
                    <div className="flex items-center gap-2 w-full">
                      <action.icon className="h-4 w-4 flex-shrink-0" />
                      <div className="flex-1 text-left">
                        <span className="text-sm font-medium">{action.label}</span>
                        {action.badge && (
                          <Badge variant="outline" className="text-xs ml-2">
                            {action.badge}
                          </Badge>
                        )}
                      </div>
                    </div>
                  </Link>
                </Button>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
