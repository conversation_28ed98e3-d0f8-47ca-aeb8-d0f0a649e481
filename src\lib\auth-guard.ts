import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export interface AuthResult {
  allowed: boolean
  reason?: string
  user?: any
}

/**
 * Verifica si un usuario puede acceder al sistema
 * Solo permite usuarios que:
 * 1. Ya existen en la base de datos (fueron invitados)
 * 2. Están activos
 * 3. Tienen un taller asignado
 */
export async function checkUserAccess(email: string): Promise<AuthResult> {
  try {
    // Buscar usuario en la base de datos
    const user = await prisma.user.findUnique({
      where: { email },
      include: {
        workshopAccess: {
          include: {
            workshop: true
          }
        },
        currentWorkshop: true
      }
    })

    // Si el usuario no existe, no puede acceder
    if (!user) {
      return {
        allowed: false,
        reason: 'Usuario no encontrado. Debes ser invitado por un administrador para acceder al sistema.'
      }
    }

    // Si el usuario está inactivo, no puede acceder
    if (!user.isActive) {
      return {
        allowed: false,
        reason: 'Tu cuenta está desactivada. Contacta al administrador.'
      }
    }

    // Si el usuario no tiene talleres asignados, no puede acceder
    if (!user.workshopAccess || user.workshopAccess.length === 0) {
      return {
        allowed: false,
        reason: 'No tienes talleres asignados. Contacta al administrador.'
      }
    }

    // Si el usuario no tiene un taller actual, asignar el primero disponible
    if (!user.currentWorkshopId) {
      const firstWorkshop = user.workshopAccess.find(wa => wa.isActive)?.workshop
      if (firstWorkshop) {
        await prisma.user.update({
          where: { id: user.id },
          data: { currentWorkshopId: firstWorkshop.id }
        })
        user.currentWorkshopId = firstWorkshop.id
        user.currentWorkshop = firstWorkshop
      }
    }

    return {
      allowed: true,
      user
    }

  } catch (error) {
    console.error('Error checking user access:', error)
    return {
      allowed: false,
      reason: 'Error interno del sistema. Intenta más tarde.'
    }
  }
}

/**
 * Verifica si un email tiene una invitación pendiente
 */
export async function checkPendingInvitation(email: string): Promise<boolean> {
  try {
    const invitation = await prisma.invitation.findFirst({
      where: {
        email,
        status: 'PENDING',
        expiresAt: {
          gt: new Date()
        }
      }
    })

    return !!invitation
  } catch (error) {
    console.error('Error checking pending invitation:', error)
    return false
  }
}

/**
 * Acepta una invitación y crea/actualiza el usuario
 */
export async function acceptInvitation(email: string, userData: {
  id: string
  name: string
  image?: string
}): Promise<AuthResult> {
  try {
    // Buscar invitación pendiente
    const invitation = await prisma.invitation.findFirst({
      where: {
        email,
        status: 'PENDING',
        expiresAt: {
          gt: new Date()
        }
      }
    })

    if (!invitation) {
      return {
        allowed: false,
        reason: 'No tienes una invitación válida.'
      }
    }

    // Crear o actualizar usuario
    const user = await prisma.user.upsert({
      where: { email },
      update: {
        name: userData.name,
        image: userData.image,
        isActive: true,
        emailVerified: true,
        updatedAt: new Date()
      },
      create: {
        id: userData.id,
        name: userData.name,
        email: email,
        image: userData.image,
        role: invitation.role,
        isActive: true,
        emailVerified: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    })

    // Marcar invitación como aceptada
    await prisma.invitation.update({
      where: { id: invitation.id },
      data: { status: 'ACCEPTED' }
    })

    // TODO: Asignar usuario al taller correspondiente
    // Por ahora, asignar al taller por defecto
    const defaultWorkshop = await prisma.workshop.findFirst({
      where: { isActive: true }
    })

    if (defaultWorkshop) {
      await prisma.userWorkshop.upsert({
        where: {
          userId_workshopId: {
            userId: user.id,
            workshopId: defaultWorkshop.id
          }
        },
        update: {
          role: invitation.role,
          isActive: true
        },
        create: {
          userId: user.id,
          workshopId: defaultWorkshop.id,
          role: invitation.role,
          isActive: true,
          assignedAt: new Date()
        }
      })

      await prisma.user.update({
        where: { id: user.id },
        data: { currentWorkshopId: defaultWorkshop.id }
      })
    }

    return {
      allowed: true,
      user
    }

  } catch (error) {
    console.error('Error accepting invitation:', error)
    return {
      allowed: false,
      reason: 'Error procesando la invitación. Intenta más tarde.'
    }
  }
}
