import { t } from 'elysia'

export const CreateInvitationSchema = t.Object({
  email: t.String({ format: 'email' }),
  role: t.<PERSON>([
    t.Literal('ADMIN'),
    t.Literal('RECEPCIONISTA'),
    t.<PERSON>('TECNICO'),
    t.<PERSON>('JEFE_TALLER'),
    t.<PERSON>('COMPRAS'),
    t.<PERSON>('FACTURACION')
  ]),
  workshopIds: t.<PERSON>(t.String())
})

export const InvitationResponseSchema = t.Object({
  id: t.String(),
  email: t.String(),
  role: t.String(),
  status: t.String(),
  expiresAt: t.Date(),
  createdAt: t.Date()
})

export type CreateInvitationInput = typeof CreateInvitationSchema.static
export type InvitationResponse = typeof InvitationResponseSchema.static
