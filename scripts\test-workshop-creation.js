const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function testWorkshopCreation() {
  console.log('🧪 Probando creación de taller...')

  try {
    // Create a test workshop
    const workshop = await prisma.workshop.create({
      data: {
        name: 'Taller de Prueba',
        location: 'Calle Falsa 123, Ciudad de Prueba',
        phone: '+52 55 9999 9999',
        email: '<EMAIL>',
        isActive: true,
        createdById: 'admin-user-id', // Use the admin user ID
        settings: {
          businessHours: {
            monday: { open: '08:00', close: '18:00' },
            tuesday: { open: '08:00', close: '18:00' },
            wednesday: { open: '08:00', close: '18:00' },
            thursday: { open: '08:00', close: '18:00' },
            friday: { open: '08:00', close: '18:00' },
            saturday: { open: '08:00', close: '14:00' },
            sunday: { closed: true }
          },
          currency: 'MXN',
          timezone: 'America/Mexico_City',
          features: {
            photoEvidence: true,
            diagnostics: true,
            inventory: true,
            billing: true
          }
        }
      }
    })

    console.log('✅ Taller creado:', workshop.name, workshop.id)

    // Get all admin users
    const adminUsers = await prisma.user.findMany({
      where: {
        role: 'ADMIN',
        isActive: true
      }
    })

    console.log('✅ Admins encontrados:', adminUsers.length)

    // Assign all admins to the workshop
    for (const admin of adminUsers) {
      await prisma.userWorkshop.create({
        data: {
          userId: admin.id,
          workshopId: workshop.id,
          role: 'ADMIN',
          isActive: true,
          assignedAt: new Date()
        }
      })
      console.log(`✅ Admin ${admin.name} asignado al taller`)
    }

    console.log('🎉 Taller de prueba creado exitosamente!')
    console.log(`   • ID: ${workshop.id}`)
    console.log(`   • Nombre: ${workshop.name}`)
    console.log(`   • Ubicación: ${workshop.location}`)
    console.log(`   • Admins asignados: ${adminUsers.length}`)

  } catch (error) {
    console.error('❌ Error creando taller de prueba:', error)
  } finally {
    await prisma.$disconnect()
  }
}

testWorkshopCreation()
