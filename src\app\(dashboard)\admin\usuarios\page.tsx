"use client"

import { useState } from 'react'
import { useIsAdmin } from '@/hooks/use-role'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { DashboardHeader } from '@/components/ui/dashboard-header'
import { StatusBadge } from '@/components/status-badge'
import { 
  Users, 
  Plus, 
  Search, 
  Mail, 
  Building2,
  Settings,
  Edit,
  Trash2,
  Shield,
  UserCheck,
  UserX
} from 'lucide-react'
import Link from 'next/link'
import { UserRole } from '@/types'

interface User {
  id: string
  name: string
  email: string
  role: UserRole
  isActive: boolean
  workshops: {
    id: string
    name: string
    role: UserRole
  }[]
  createdAt: string
  lastLogin?: string
}

const roleLabels = {
  [UserRole.ADMIN]: 'Administrador',
  [UserRole.RECEPCIONISTA]: 'Recepcion<PERSON>',
  [UserRole.TECNICO]: 'Técnico',
  [UserRole.JEFE_TALLER]: '<PERSON><PERSON>',
  [UserRole.COMPRAS]: 'Compras',
  [UserRole.FACTURACION]: 'Facturación'
}

const roleColors = {
  [UserRole.ADMIN]: 'bg-red-100 text-red-800 border-red-200',
  [UserRole.RECEPCIONISTA]: 'bg-blue-100 text-blue-800 border-blue-200',
  [UserRole.TECNICO]: 'bg-green-100 text-green-800 border-green-200',
  [UserRole.JEFE_TALLER]: 'bg-purple-100 text-purple-800 border-purple-200',
  [UserRole.COMPRAS]: 'bg-orange-100 text-orange-800 border-orange-200',
  [UserRole.FACTURACION]: 'bg-yellow-100 text-yellow-800 border-yellow-200'
}

export default function UsersPage() {
  const isAdmin = useIsAdmin()
  const [search, setSearch] = useState('')
  const [roleFilter, setRoleFilter] = useState<UserRole | ''>('')

  // Redirect if not admin
  if (!isAdmin) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5 text-destructive" />
              Acceso Denegado
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-4">
              No tienes permisos para gestionar usuarios.
            </p>
            <Button asChild>
              <Link href="/admin">
                Volver al Panel de Admin
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Mock data for users - replace with real API call
  const users: User[] = [
    {
      id: "admin-user-id",
      name: "Administrador",
      email: "<EMAIL>",
      role: UserRole.ADMIN,
      isActive: true,
      workshops: [
        { id: "default-workshop-ruedda", name: "Ruedda", role: UserRole.ADMIN }
      ],
      createdAt: "2024-01-15T10:00:00Z",
      lastLogin: "2024-01-20T14:30:00Z"
    },
    {
      id: "receptionist-user-id",
      name: "María Recepcionista",
      email: "<EMAIL>",
      role: UserRole.RECEPCIONISTA,
      isActive: true,
      workshops: [
        { id: "default-workshop-ruedda", name: "Ruedda", role: UserRole.RECEPCIONISTA }
      ],
      createdAt: "2024-01-15T10:00:00Z",
      lastLogin: "2024-01-20T09:15:00Z"
    },
    {
      id: "technician-user-id",
      name: "Carlos Técnico",
      email: "<EMAIL>",
      role: UserRole.TECNICO,
      isActive: true,
      workshops: [
        { id: "default-workshop-ruedda", name: "Ruedda", role: UserRole.TECNICO }
      ],
      createdAt: "2024-01-15T10:00:00Z",
      lastLogin: "2024-01-19T16:45:00Z"
    }
  ]

  const filteredUsers = users.filter(user => {
    const matchesSearch = user.name.toLowerCase().includes(search.toLowerCase()) ||
                         user.email.toLowerCase().includes(search.toLowerCase())
    const matchesRole = !roleFilter || user.role === roleFilter
    return matchesSearch && matchesRole
  })

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('es-MX', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <div className="p-6">
      <div className="max-w-7xl mx-auto">
        <div className="flex items-center justify-between mb-8">
          <DashboardHeader
            title="Gestión de Usuarios"
            description="Administra usuarios, roles y permisos del sistema"
            showBackButton={true}
          />
          <Button asChild size="lg">
            <Link href="/admin/invitatciones">
              <Plus className="w-5 h-5 mr-2" />
              Invitar Usuario
            </Link>
          </Button>
        </div>

        {/* Search and Filters */}
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="Buscar usuarios por nombre o email..."
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
              <div className="flex gap-2">
                <Button
                  variant={roleFilter === '' ? 'default' : 'outline'}
                  onClick={() => setRoleFilter('')}
                  size="sm"
                >
                  Todos
                </Button>
                {Object.entries(roleLabels).map(([role, label]) => (
                  <Button
                    key={role}
                    variant={roleFilter === role ? 'default' : 'outline'}
                    onClick={() => setRoleFilter(role as UserRole)}
                    size="sm"
                  >
                    {label}
                  </Button>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Users List */}
        {filteredUsers.length === 0 ? (
          <Card>
            <CardContent className="text-center py-12">
              <Users className="h-16 w-16 mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {search || roleFilter ? 'No se encontraron usuarios' : 'No hay usuarios registrados'}
              </h3>
              <p className="text-gray-500 mb-6">
                {search || roleFilter
                  ? 'Intenta con otros filtros de búsqueda'
                  : 'Comienza invitando usuarios al sistema'
                }
              </p>
              {!search && !roleFilter && (
                <Button asChild>
                  <Link href="/admin/invitatciones">
                    <Plus className="w-4 h-4 mr-2" />
                    Invitar Primer Usuario
                  </Link>
                </Button>
              )}
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-4">
            {filteredUsers.map((user) => (
              <Card key={user.id} className="hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
                        <Users className="h-6 w-6 text-primary" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-lg">{user.name}</h3>
                        <div className="flex items-center gap-2 mt-1">
                          <Mail className="h-4 w-4 text-muted-foreground" />
                          <span className="text-muted-foreground">{user.email}</span>
                        </div>
                        <div className="flex items-center gap-2 mt-2">
                          <Badge className={roleColors[user.role]}>
                            {roleLabels[user.role]}
                          </Badge>
                          <Badge variant={user.isActive ? "default" : "secondary"}>
                            {user.isActive ? "Activo" : "Inactivo"}
                          </Badge>
                          {user.workshops.map((workshop) => (
                            <Badge key={workshop.id} variant="outline" className="text-xs">
                              <Building2 className="w-3 h-3 mr-1" />
                              {workshop.name}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <div className="text-right text-sm text-muted-foreground mr-4">
                        <p>Creado: {formatDate(user.createdAt)}</p>
                        {user.lastLogin && (
                          <p>Último acceso: {formatDate(user.lastLogin)}</p>
                        )}
                      </div>
                      <Button size="sm" variant="outline">
                        <Edit className="w-4 h-4 mr-1" />
                        Editar
                      </Button>
                      <Button size="sm" variant="outline">
                        <Settings className="w-4 h-4 mr-1" />
                        Permisos
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Summary Stats */}
        {filteredUsers.length > 0 && (
          <Card className="mt-8">
            <CardHeader>
              <CardTitle>Resumen de Usuarios</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary">{users.length}</div>
                  <p className="text-sm text-muted-foreground">Total Usuarios</p>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {users.filter(u => u.isActive).length}
                  </div>
                  <p className="text-sm text-muted-foreground">Usuarios Activos</p>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">
                    {users.filter(u => u.role === UserRole.ADMIN).length}
                  </div>
                  <p className="text-sm text-muted-foreground">Administradores</p>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {users.filter(u => u.role === UserRole.RECEPCIONISTA).length}
                  </div>
                  <p className="text-sm text-muted-foreground">Recepcionistas</p>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {users.filter(u => u.role === UserRole.TECNICO).length}
                  </div>
                  <p className="text-sm text-muted-foreground">Técnicos</p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
