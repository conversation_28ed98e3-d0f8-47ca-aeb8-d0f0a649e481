@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  /* Updated to exact yellow #F9D03B and black secondary colors */
  --radius: 0.5rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.15 0 0);
  --card: oklch(0.98 0.01 85);
  --card-foreground: oklch(0.15 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.15 0 0);
  --primary: oklch(0.82 0.12 85); /* #F9D03B yellow */
  --primary-foreground: oklch(0.15 0 0);
  --secondary: oklch(0.15 0 0); /* Black secondary */
  --secondary-foreground: oklch(1 0 0);
  --muted: oklch(0.96 0.01 85);
  --muted-foreground: oklch(0.45 0 0);
  --accent: oklch(0.82 0.12 85);
  --accent-foreground: oklch(0.15 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: oklch(1 0 0);
  --border: oklch(0.9 0.01 85);
  --input: oklch(1 0 0);
  --ring: oklch(0.82 0.12 85 / 0.5);
  --chart-1: oklch(0.82 0.12 85);
  --chart-2: oklch(0.15 0 0);
  --chart-3: oklch(0.6 0.15 140);
  --chart-4: oklch(0.7 0.2 320);
  --chart-5: oklch(0.5 0.15 200);
  --sidebar: oklch(0.98 0.01 85);
  --sidebar-foreground: oklch(0.15 0 0);
  --sidebar-primary: oklch(0.82 0.12 85);
  --sidebar-primary-foreground: oklch(0.15 0 0);
  --sidebar-accent: oklch(0.96 0.01 85);
  --sidebar-accent-foreground: oklch(0.15 0 0);
  --sidebar-border: oklch(0.9 0.01 85);
  --sidebar-ring: oklch(0.82 0.12 85 / 0.3);
}

.dark {
  /* Updated dark mode with exact yellow and black theme */
  --background: oklch(0.08 0 0);
  --foreground: oklch(0.95 0 0);
  --card: oklch(0.12 0 0);
  --card-foreground: oklch(0.95 0 0);
  --popover: oklch(0.08 0 0);
  --popover-foreground: oklch(0.95 0 0);
  --primary: oklch(0.82 0.12 85); /* Same yellow in dark mode */
  --primary-foreground: oklch(0.08 0 0);
  --secondary: oklch(0.18 0 0);
  --secondary-foreground: oklch(0.95 0 0);
  --muted: oklch(0.18 0 0);
  --muted-foreground: oklch(0.65 0 0);
  --accent: oklch(0.82 0.12 85);
  --accent-foreground: oklch(0.08 0 0);
  --destructive: oklch(0.6 0.25 25);
  --destructive-foreground: oklch(0.95 0 0);
  --border: oklch(0.18 0 0);
  --input: oklch(0.12 0 0);
  --ring: oklch(0.82 0.12 85 / 0.5);
  --chart-1: oklch(0.82 0.12 85);
  --chart-2: oklch(0.4 0.1 200);
  --chart-3: oklch(0.6 0.15 140);
  --chart-4: oklch(0.7 0.2 320);
  --chart-5: oklch(0.5 0.15 60);
  --sidebar: oklch(0.08 0 0);
  --sidebar-foreground: oklch(0.95 0 0);
  --sidebar-primary: oklch(0.82 0.12 85);
  --sidebar-primary-foreground: oklch(0.08 0 0);
  --sidebar-accent: oklch(0.15 0 0);
  --sidebar-accent-foreground: oklch(0.95 0 0);
  --sidebar-border: oklch(0.18 0 0);
  --sidebar-ring: oklch(0.82 0.12 85 / 0.3);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
  button,
  select {
    @apply cursor-pointer;
  }
}
