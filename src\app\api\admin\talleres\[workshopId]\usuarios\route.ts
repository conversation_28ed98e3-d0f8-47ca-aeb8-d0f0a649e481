import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient, UserRole } from '@prisma/client'
import { getServerSession } from '@/lib/getSession'

const prisma = new PrismaClient()

export async function GET(
  request: NextRequest,
  { params }: { params: { workshopId: string } }
) {
  try {
    // Verify user is authenticated and is admin
    const session = await getServerSession({ shouldRedirect: false })
    
    if (!session || session.user.role !== UserRole.ADMIN) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin access required.' },
        { status: 403 }
      )
    }

    const { workshopId } = params

    // Verify workshop exists
    const workshop = await prisma.workshop.findUnique({
      where: { id: workshopId }
    })

    if (!workshop) {
      return NextResponse.json(
        { error: 'Workshop not found' },
        { status: 404 }
      )
    }

    // Get workshop users
    const workshopUsers = await prisma.userWorkshop.findMany({
      where: {
        workshopId: workshopId
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
            emailVerified: true,
            createdAt: true,
            isActive: true
          }
        }
      },
      orderBy: {
        assignedAt: 'desc'
      }
    })

    const users = workshopUsers.map(uw => ({
      id: uw.id,
      name: uw.user.name,
      email: uw.user.email,
      role: uw.role,
      isActive: uw.isActive,
      assignedAt: uw.assignedAt,
      user: {
        image: uw.user.image,
        emailVerified: uw.user.emailVerified,
        createdAt: uw.user.createdAt
      }
    }))

    return NextResponse.json({
      success: true,
      users
    })

  } catch (error) {
    console.error('Error fetching workshop users:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
