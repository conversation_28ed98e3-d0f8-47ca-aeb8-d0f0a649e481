"use client"
import { useWorkshop } from '@/contexts/workshop-context'
import { But<PERSON> } from '@/components/ui/button'
import { Combobox, ComboboxOption } from '@/components/ui/combobox'
import {
  Building2,
  ChevronDown,
  MapPin,
  Plus,
  Globe,
  Eye
} from 'lucide-react'

import Link from 'next/link'

interface Workshop {
  id: string
  name: string
  location: string
  isActive: boolean
  role: string
}

export function WorkshopSwitcher() {
  const {
    currentWorkshop,
    availableWorkshops,
    isGlobalView,
    canToggleGlobalView,
    switchWorkshop,
    isLoading: contextLoading,
    isLoadingWorkshops
  } = useWorkshop()


  // Determine current display state
  const displayText = isGlobalView
    ? "Vista Global - Todos los Talleres"
    : currentWorkshop?.name || "Seleccionar Taller"
  const displayLocation = isGlobalView
    ? "Viendo información de todos los talleres"
    : currentWorkshop?.location || ""

  // Prepare combobox options
  const comboboxOptions: ComboboxOption[] = [
    // Global view option for admins
    ...(canToggleGlobalView ? [{
      value: '',
      label: 'Vista Global',
      description: 'Ver todos los talleres',
      icon: <Globe className="h-4 w-4 text-blue-500" />
    }] : []),
    // Workshop options
    ...availableWorkshops.map(workshop => ({
      value: workshop.id,
      label: workshop.name,
      description: workshop.location,
      icon: <Building2 className="h-4 w-4 text-primary" />
    }))
  ]

  const currentValue = isGlobalView ? '' : currentWorkshop?.id || ''

  const handleWorkshopChange = async (workshopId: string) => {
    // console.log('handleWorkshopChange:', workshopId)
    try {
      // If empty string is passed, it means global view (only for admins)
      console.log('is empty:', workshopId === '', 'canToggleGlobalView:', canToggleGlobalView)
      if (workshopId === '' && canToggleGlobalView) {
        await switchWorkshop(null)
      } else {
        console.log('handleWorkshopChange: Switching to workshop:', workshopId)
        await switchWorkshop(workshopId)
      }
      // Combobox closes automatically after selection
    } catch (error) {
      console.error('Error switching workshop:', error)
    }
  }

  // For non-admin users without workshop, show message
  if (!currentWorkshop && !canToggleGlobalView) {
    return (
      <div className="px-3 py-2">
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <Building2 className="h-4 w-4" />
          <span>Sin taller asignado</span>
        </div>
      </div>
    )
  }

  const customTrigger = (
    <Button
      variant="ghost"
      className="w-full justify-between h-auto p-2 hover:bg-accent"
    >
      <div className="flex items-center gap-2 min-w-0">
        {isGlobalView ? (
          <Globe className="h-4 w-4 text-blue-500 flex-shrink-0" />
        ) : (
          <Building2 className="h-4 w-4 text-primary flex-shrink-0" />
        )}
        <div className="text-left min-w-0">
          <div className="font-medium text-sm truncate">
            {displayText}
          </div>
          <div className="flex items-center gap-1 text-xs text-muted-foreground">
            {isGlobalView ? (
              <Eye className="h-3 w-3 flex-shrink-0" />
            ) : (
              <MapPin className="h-3 w-3 flex-shrink-0" />
            )}
            <span className="truncate">{displayLocation}</span>
          </div>
        </div>
      </div>
      <ChevronDown className="h-4 w-4 flex-shrink-0" />
    </Button>
  )

  return (
    <div className="px-3 py-2">
      <div className="space-y-2">
        <Combobox
          options={comboboxOptions}
          value={currentValue}
          onValueChange={handleWorkshopChange}
          placeholder="Seleccionar Taller"
          searchPlaceholder="Buscar taller..."
          emptyText="No se encontraron talleres"
          loading={isLoadingWorkshops || contextLoading}
          trigger={customTrigger}
          className="w-full"
        />

        {canToggleGlobalView && (
          <div className="pt-2 border-t">
            <Button
              variant="ghost"
              size="sm"
              className="w-full justify-start text-xs"
              asChild
            >
              <Link href="/admin/talleres" className="flex items-center gap-2">
                <Plus className="h-3 w-3" />
                <span>Gestionar Talleres</span>
              </Link>
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}
