"use client"

import { UserRole } from "@/types"
import { useRole, useIsAdmin } from "@/hooks/use-role"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { User } from "lucide-react"

const roleLabels: Record<UserRole, string> = {
  [UserRole.ADMIN]: "Administrador",
  [UserRole.RECEPCIONISTA]: "Recepcionista", 
  [UserRole.TECNICO]: "Técnico",
  [UserRole.JEFE_TALLER]: "Je<PERSON> de <PERSON>",
  [UserRole.COMPRAS]: "Compras",
  [UserRole.FACTURACION]: "Facturación",
}

const roleColors: Record<UserRole, string> = {
  [UserRole.ADMIN]: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
  [UserRole.RECEPCIONISTA]: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
  [UserRole.TECNICO]: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
  [UserRole.JEFE_TALLER]: "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200",
  [UserRole.COMPRAS]: "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200",
  [UserRole.FACTURACION]: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
}

interface RoleSelectorProps {
  currentRole: UserRole
  onRoleChange: (role: UserRole) => void
  disabled?: boolean
  showBadge?: boolean
}

export function RoleSelector({ 
  currentRole, 
  onRoleChange, 
  disabled = false,
  showBadge = true 
}: RoleSelectorProps) {
  const isAdmin = useIsAdmin()

  // Only admins can change roles, and they can't change their own role in production
  const canChangeRole = isAdmin && !disabled

  if (showBadge && !canChangeRole) {
    return (
      <div className="flex items-center gap-2">
        <User className="h-4 w-4 text-muted-foreground" />
        <Badge className={roleColors[currentRole]}>
          {roleLabels[currentRole]}
        </Badge>
      </div>
    )
  }

  return (
    <div className="flex items-center gap-2">
      <User className="h-4 w-4 text-muted-foreground" />
      {canChangeRole ? (
        <Select
          value={currentRole}
          onValueChange={(value) => onRoleChange(value as UserRole)}
          disabled={disabled}
        >
          <SelectTrigger className="w-[180px]">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {Object.entries(roleLabels).map(([role, label]) => (
              <SelectItem key={role} value={role}>
                <div className="flex items-center gap-2">
                  <Badge className={roleColors[role as UserRole]} variant="outline">
                    {label}
                  </Badge>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      ) : (
        <Badge className={roleColors[currentRole]}>
          {roleLabels[currentRole]}
        </Badge>
      )}
    </div>
  )
}

// Simple role badge component
export function RoleBadge({ role }: { role: UserRole }) {
  return (
    <Badge className={roleColors[role]}>
      {roleLabels[role]}
    </Badge>
  )
}
