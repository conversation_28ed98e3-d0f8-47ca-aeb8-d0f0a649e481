import { useCurrentWorkshopId } from '@/contexts/workshop-context'

/**
 * Hook to get the current workshop ID for use in React Query keys
 * This ensures that queries are automatically invalidated when the workshop changes
 */
export function useWorkshopQuery() {
  const currentWorkshopId = useCurrentWorkshopId()
  
  return {
    currentWorkshopId,
    // Helper function to create query keys that include workshop context
    createQueryKey: (baseKey: string[], additionalParams?: Record<string, any>) => {
      const params = additionalParams || {}
      return [
        ...baseKey,
        {
          workshopId: currentWorkshopId,
          ...params
        }
      ]
    }
  }
}

/**
 * Helper function to create workshop-aware query keys
 * Usage: createWorkshopQueryKey(['clients'], { search: 'john' })
 * Result: ['clients', { workshopId: 'workshop-id', search: 'john' }]
 */
export function createWorkshopQueryKey(baseKey: string[], additionalParams?: Record<string, any>) {
  // This is a standalone function that can be used without the hook
  // but it won't be reactive to workshop changes
  return (workshopId: string | null, params?: Record<string, any>) => {
    const finalParams = { ...additionalParams, ...params }
    return [
      ...baseKey,
      {
        workshopId,
        ...finalParams
      }
    ]
  }
}
