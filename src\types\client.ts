// Client types adapted from example project to work with current Prisma schema

export enum ClientType {
  INDIVIDUAL = "INDIVIDUAL",
  FLEET = "FLEET", // Changed from FLOTILLERO to match current schema
}

// Client interface matching current Prisma schema
export interface Client {
  id: string
  name: string
  phone: string
  email?: string
  clientType: ClientType

  // Tax information (optional for individuals, required for fleet)
  businessName?: string // Maps to businessName in schema
  taxId?: string // Maps to taxId in schema  
  address?: string

  // Fleet configuration
  paymentTerms?: string
  billingPeriod?: string

  // Metadata
  createdAt: Date
  updatedAt: Date
  createdById: string

  // Relations (populated when needed)
  vehicles?: Vehicle[]
  receptions?: Reception[]
  createdBy?: User
}

// Client creation data
export interface CreateClientData {
  name: string
  phone: string
  email?: string
  clientType: ClientType
  businessName?: string
  taxId?: string
  address?: string
  paymentTerms?: string
  billingPeriod?: string
}

// Client update data
export interface UpdateClientData {
  name?: string
  phone?: string
  email?: string
  clientType?: ClientType
  businessName?: string
  taxId?: string
  address?: string
  paymentTerms?: string
  billingPeriod?: string
}

// Client search/filter interface
export interface ClientFilters {
  search?: string
  clientType?: ClientType
  createdById?: string
  dateFrom?: Date
  dateTo?: Date
}

// Import types that will be used in relations
import type { Vehicle } from './vehicle'
import type { Reception } from './reception'
import type { User } from './user'
