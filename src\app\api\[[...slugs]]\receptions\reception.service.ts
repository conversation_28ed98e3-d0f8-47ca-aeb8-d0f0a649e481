import db from "@/config/db";
import { CreateReceptionInput, UpdateReceptionInput } from "./reception.dto";
import { HttpException } from "@/lib/exceptions/HttpExceptions";



export class ReceptionService {

  static async create(data: CreateReceptionInput, receptionistId: string, workshopId: string) {
    // Data is already in English format from DTO
    const receptionData = {
      mileage: data.mileage,
      serviceType: data.serviceType as any,
      otherService: data.otherService,
      observations: data.observations,
      tiresCondition: data.tiresCondition as any,
      assignedTech: data.assignedTech,
      clientId: data.clientId,
      vehicleId: data.vehicleId,
      receptionistId: receptionistId,
      workshopId
    };

    // Check that the client exists
    const client = await db.client.findUnique({
      where: { id: receptionData.clientId }
    });

    if (!client) {
      throw HttpException.NotFound("Cliente no encontrado");
    }

    // Check that the vehicle exists and belongs to the client
    const vehicle = await db.vehicle.findFirst({
      where: {
        id: receptionData.vehicleId,
        clientId: receptionData.clientId
      }
    });

    if (!vehicle) {
      throw HttpException.NotFound("Vehículo no encontrado o no pertenece al cliente especificado");
    }

    // Check that there's no active reception for this vehicle
    const activeReception = await db.reception.findFirst({
      where: {
        vehicleId: receptionData.vehicleId,
        status: {
          in: ['RECEIVED', 'IN_DIAGNOSTIC', 'WAITING_APPROVAL', 'IN_REPAIR']
        }
      }
    });

    if (activeReception) {
      throw HttpException.Conflict("Este vehículo ya tiene una recepción activa en el taller");
    }

    // Create the reception
    const reception = await db.reception.create({
      data: {
        ...receptionData,
        evidence: {
          create: data.evidence.map((evidence: any) => ({
            type: evidence.type as any,
            url: evidence.url,
            description: evidence.description
          }))
        }
      },
      include: {
        client: {
          select: {
            id: true,
            name: true,
            phone: true,
            clientType: true
          }
        },
        vehicle: {
          select: {
            id: true,
            brand: true,
            model: true,
            year: true,
            plates: true,
            color: true
          }
        },
        receptionist: {
          select: {
            name: true,
            email: true
          }
        },
        evidence: true
      }
    });

    // Map back to frontend format
    const mappedReception = {
      id: reception.id,
      fechaEntrada: reception.entryDate,
      kilometraje: reception.mileage,
      servicioTipo: reception.serviceType,
      servicioOtro: reception.otherService,
      observaciones: reception.observations,
      estadoLlantas: reception.tiresCondition,
      tecnicoAsignado: reception.assignedTech,
      status: reception.status,
      clienteId: reception.clientId,
      cliente: {
        id: reception.client.id,
        nombre: reception.client.name,
        telefono: reception.client.phone,
        tipoCliente: reception.client.clientType
      },
      vehiculoId: reception.vehicleId,
      vehiculo: {
        id: reception.vehicle.id,
        marca: reception.vehicle.brand,
        modelo: reception.vehicle.model,
        año: reception.vehicle.year,
        placas: reception.vehicle.plates,
        color: reception.vehicle.color
      },
      recepcionistaId: reception.receptionistId,
      recepcionista: reception.receptionist,
      evidencias: reception.evidence.map((ev: any) => ({
        id: ev.id,
        tipo: ev.type,
        url: ev.url,
        descripcion: ev.description
      })),
      createdAt: reception.createdAt,
      updatedAt: reception.updatedAt
    };

    return mappedReception;
  }

  static async list(filters?: {
    status?: string;
    clientId?: string;
    vehicleId?: string;
    assignedTech?: string;
    dateFrom?: Date;
    dateTo?: Date;
    workshopId?: string | null;
  }) {
    const where: any = {};

    // Filter by workshop - if null, show all (for admin global view)
    if (filters?.workshopId !== undefined) {
      if (filters.workshopId === null) {
        // Global view - no workshop filter
      } else {
        where.workshopId = filters.workshopId;
      }
    }

    if (filters?.status) {
      where.status = filters.status;
    }

    if (filters?.clientId) {
      where.clientId = filters.clientId;
    }

    if (filters?.vehicleId) {
      where.vehicleId = filters.vehicleId;
    }

    if (filters?.assignedTech) {
      where.assignedTech = filters.assignedTech;
    }

    if (filters?.dateFrom || filters?.dateTo) {
      where.entryDate = {};
      if (filters.dateFrom) {
        where.entryDate.gte = filters.dateFrom;
      }
      if (filters.dateTo) {
        where.entryDate.lte = filters.dateTo;
      }
    }
console.log('Where [reception.service.ts]:', where);
    return await db.reception.findMany({
      where,
      include: {
        client: {
          select: {
            id: true,
            name: true,
            phone: true,
            clientType: true
          }
        },
        vehicle: {
          select: {
            id: true,
            brand: true,
            model: true,
            year: true,
            plates: true,
            color: true
          }
        },
        receptionist: {
          select: {
            name: true,
            email: true
          }
        },
        evidence: true,
        _count: {
          select: {
            evidence: true
          }
        }
      },
      orderBy: {
        entryDate: 'desc'
      }
    });
  }

  static async getById(id: string) {
    const reception = await db.reception.findUnique({
      where: { id },
      include: {
        client: true,
        vehicle: true,
        receptionist: {
          select: {
            name: true,
            email: true,
            role: true
          }
        },
        evidence: {
          orderBy: [
            { type: 'asc' },
            { createdAt: 'asc' }
          ]
        }
      }
    });

    if (!reception) {
      throw new Error("Recepción no encontrada");
    }

    return reception;
  }

  static async update(id: string, data: UpdateReceptionInput) {
    const existingRecepcion = await db.reception.findUnique({
      where: { id }
    });

    if (!existingRecepcion) {
      throw new Error("Recepción no encontrada");
    }

    return await db.reception.update({
      where: { id },
      data,
      include: {
        client: {
          select: {
            id: true,
            name: true,
            phone: true,
            clientType: true
          }
        },
        vehicle: {
          select: {
            id: true,
            brand: true,
            model: true,
            year: true,
            plates: true,
            color: true
          }
        },
        receptionist: {
          select: {
            name: true,
            email: true
          }
        },
        evidence: true,
        _count: {
          select: {
            evidence: true
          }
        }
      }
    });
  }

  static async delete(id: string) {
    const reception = await db.reception.findUnique({
      where: { id },
      include: {
        evidence: true
      }
    });

    if (!reception) {
      throw new Error("Recepción no encontrada");
    }

    // Solo permitir eliminar recepciones que no han avanzado mucho en el proceso
    if (!['RECEIVED', 'IN_DIAGNOSTIC'].includes(reception.status)) {
      throw new Error("No se puede eliminar una recepción que ya ha avanzado en el proceso");
    }

    return await db.reception.delete({
      where: { id }
    });
  }

  static async getActive() {
    return await db.reception.findMany({
      where: {
        status: {
          in: ['RECEIVED', 'IN_DIAGNOSTIC', 'WAITING_APPROVAL', 'IN_REPAIR']
        }
      },
      include: {
        client: {
          select: {
            id: true,
            name: true,
            phone: true,
            clientType: true
            // nombre: true,
            // telefono: true,
            // tipoCliente: true
          }
        },
        vehicle: {
          select: {
            id: true,
            brand: true,
            model: true,
            year: true,
            plates: true,
            color: true
          }
        },
        receptionist: {
          select: {
            name: true,
            email: true
          }
        }
      },
      orderBy: {
        entryDate: 'asc'
      }
    });
  }

  static async getStats() {
    const [total, received, inDiagnostic, inRepair, completed] = await Promise.all([
      db.reception.count(),
      db.reception.count({ where: { status: 'RECEIVED' } }),
      db.reception.count({ where: { status: 'IN_DIAGNOSTIC' } }),
      db.reception.count({ where: { status: 'IN_REPAIR' } }),
      db.reception.count({ where: { status: 'COMPLETED' } })
    ]);

    return {
      total,
      received,
      inDiagnostic,
      inRepair,
      completed,
      active: received + inDiagnostic + inRepair
    };
  }
}
