import { getServerSession } from '@/lib/getSession'
import { redirect } from 'next/navigation'
import { DashboardLayout as DashboardLayoutComponent } from '@/components/dashboard-layout'
import { checkUserAccess } from '@/lib/auth-guard'
import { RoleProvider } from '@/hooks/use-role'
import { WorkshopProvider } from '@/contexts/workshop-context'

export default async function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const session = await getServerSession()
  session.user.role
  if (!session) {
    return redirect('/(auth)/login')
  }

  // Check if user has access to the system
  const accessResult = await checkUserAccess(session.user.email)

  if (!accessResult.allowed) {
    const params = new URLSearchParams({
      reason: accessResult.reason || 'Acceso denegado',
      email: session.user.email
    })
    return redirect(`/access-denied?${params.toString()}`)
  }


  return (
    <>
    <RoleProvider
                initialSession={session}
              >
                <WorkshopProvider>

    <DashboardLayoutComponent>
      {children}
    </DashboardLayoutComponent>
                </WorkshopProvider>
                </RoleProvider>
    </>
  )
}
