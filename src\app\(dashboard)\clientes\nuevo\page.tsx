'use client'

import { useRouter } from 'next/navigation'
import { DashboardHeader } from '@/components/ui/dashboard-header'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { server } from '@/app/api/server'
import toast from 'react-hot-toast'
import { ClientForm } from '@/components/forms/client-form'
import { type CreateClientData } from '@/types'
import { ReceptionGuard } from '@/components/role-guard'

export default function NewClientPage() {
  const router = useRouter()
  const queryClient = useQueryClient()

  const createClientMutation = useMutation({
    mutationFn: async (data: CreateClientData) => {
      console.log('Enviando datos:', data)
        const res = await server.api.clients.post(data)
        if(res.error){
          throw new Error(res.error.value.message)
        }
        return res.data.data;
    },
    onSuccess: (data) => {
      console.log('onSuccess data:', data)
        toast.success('Cliente creado exitosamente')
        queryClient.invalidateQueries({ queryKey: ['clientes'] })
        router.push(`/clientes/${data.id}`)
    },
    onError: (error) => {
      console.error('onError:', error)
      toast.error('Error al crear el cliente: ' + (error as any)?.message || 'Error desconocido')
    }
  })

  const handleSubmit = (data: CreateClientData) => {
    createClientMutation.mutate(data)
  }

  const handleCancel = () => {
    router.push('/clientes')
  }

  return (
    <ReceptionGuard>
      <div className="p-6">
        <div className="max-w-4xl mx-auto">
          <DashboardHeader
            title="Nuevo Cliente"
            description="Registra un nuevo cliente individual o flotillero"
            backUrl="/clientes"
          />

          <ClientForm
            onSubmit={handleSubmit}
            onCancel={handleCancel}
            isLoading={createClientMutation.isPending}
          />
        </div>
      </div>
    </ReceptionGuard>
  )
}
