"use client"

import { type Workshop, UserRole } from "@/types"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Building2, MapPin } from "lucide-react"

interface WorkshopSelectorProps {
  workshops: Workshop[]
  currentWorkshop: Workshop | null
  userRole: UserRole
  onWorkshopChange: (workshop: Workshop) => void
  disabled?: boolean
}

export function WorkshopSelector({
  workshops,
  currentWorkshop,
  userRole,
  onWorkshopChange,
  disabled = false
}: WorkshopSelectorProps) {
  // Filter active workshops
  const activeWorkshops = workshops.filter(w => w.isActive)

  // Only admins and managers can switch between workshops
  const canSwitchWorkshop = [UserRole.ADMIN, UserRole.JEFE_TALLER].includes(userRole)

  // Handle case where user has no workshop assigned
  if (!currentWorkshop) {
    return (
      <div className="space-y-2">
        <div className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
          <Building2 className="h-4 w-4" />
          Taller Actual
        </div>
        <div className="space-y-1">
          <div className="font-medium text-destructive">Sin taller asignado</div>
          <div className="text-xs text-muted-foreground">
            Contacta al administrador para asignarte a un taller
          </div>
          <Badge variant="destructive" className="text-xs">
            Sin acceso
          </Badge>
        </div>
      </div>
    )
  }

  if (!canSwitchWorkshop || activeWorkshops.length <= 1) {
    return (
      <div className="space-y-2">
        <div className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
          <Building2 className="h-4 w-4" />
          Taller Actual
        </div>
        <div className="space-y-1">
          <div className="font-medium">{currentWorkshop.name}</div>
          <div className="flex items-center gap-1 text-sm text-muted-foreground">
            <MapPin className="h-3 w-3" />
            {currentWorkshop.location}
          </div>
          <Badge variant="outline" className="text-xs">
            {currentWorkshop.isActive ? "Activo" : "Inactivo"}
          </Badge>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-2">
      <div className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
        <Building2 className="h-4 w-4" />
        Seleccionar Taller
      </div>
      <Select
        value={currentWorkshop?.id || ''}
        onValueChange={(value) => {
          const workshop = activeWorkshops.find(w => w.id === value)
          if (workshop) {
            onWorkshopChange(workshop)
          }
        }}
        disabled={disabled}
      >
        <SelectTrigger className="w-full">
          <SelectValue />
        </SelectTrigger>
        <SelectContent>
          {activeWorkshops.map((workshop) => (
            <SelectItem key={workshop.id} value={workshop.id}>
              <div className="space-y-1">
                <div className="font-medium">{workshop.name}</div>
                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                  <MapPin className="h-3 w-3" />
                  {workshop.location}
                </div>
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  )
}

// Simple workshop info component
export function WorkshopInfo({ workshop }: { workshop: Workshop }) {
  return (
    <div className="space-y-1">
      <div className="flex items-center gap-2">
        <Building2 className="h-4 w-4 text-muted-foreground" />
        <span className="font-medium">{workshop.name}</span>
      </div>
      <div className="flex items-center gap-1 text-sm text-muted-foreground ml-6">
        <MapPin className="h-3 w-3" />
        {workshop.location}
      </div>
    </div>
  )
}
