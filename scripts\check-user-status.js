const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function checkUserStatus() {
  console.log('🔍 Verificando estado del usuario...')

  try {
    // Find the Google user
    const googleUser = await prisma.user.findFirst({
      where: { 
        email: '<EMAIL>'
      },
      include: {
        workshopAccess: {
          include: {
            workshop: true
          }
        },
        currentWorkshop: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    if (!googleUser) {
      console.log('❌ Usuario de Google no encontrado')
      return
    }

    console.log('✅ Usuario encontrado:')
    console.log('   • ID:', googleUser.id)
    console.log('   • Nombre:', googleUser.name)
    console.log('   • Email:', googleUser.email)
    console.log('   • Rol:', googleUser.role)
    console.log('   • Activo:', googleUser.isActive)
    console.log('   • Current Workshop ID:', googleUser.currentWorkshopId)
    console.log('   • Current Workshop:', googleUser.currentWorkshop?.name || 'null')
    console.log('   • Workshop Access:', googleUser.workshopAccess.length, 'talleres')

    if (googleUser.workshopAccess.length > 0) {
      console.log('   • Talleres asignados:')
      googleUser.workshopAccess.forEach((wa, index) => {
        console.log(`     ${index + 1}. ${wa.workshop.name} (${wa.role}) - ${wa.isActive ? 'Activo' : 'Inactivo'}`)
      })
    }

    // Check if we need to fix the currentWorkshopId
    if (!googleUser.currentWorkshopId && googleUser.workshopAccess.length > 0) {
      const firstActiveWorkshop = googleUser.workshopAccess.find(wa => wa.isActive)
      if (firstActiveWorkshop) {
        console.log('🔧 Arreglando currentWorkshopId...')
        await prisma.user.update({
          where: { id: googleUser.id },
          data: { currentWorkshopId: firstActiveWorkshop.workshopId }
        })
        console.log('✅ currentWorkshopId actualizado a:', firstActiveWorkshop.workshop.name)
      }
    }

  } catch (error) {
    console.error('❌ Error verificando usuario:', error)
  } finally {
    await prisma.$disconnect()
  }
}

checkUserStatus()
