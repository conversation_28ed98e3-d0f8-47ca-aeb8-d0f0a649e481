'use client'

import React, { createContext, useContext, useState, useMemo } from 'react'
import { useIsAdmin, useRole } from '@/hooks/use-role'
import { server } from '@/app/api/server'
import { useQuery } from '@tanstack/react-query'
import { USER_WORKSHOPS_AVAILABLE } from '@/constants/queries-key'

interface Workshop {
  id: string
  name: string
  location: string
  isActive: boolean
  role: string
}

interface WorkshopContextType {
  currentWorkshop: Workshop | null
  availableWorkshops: Workshop[]
  isGlobalView: boolean
  canToggleGlobalView: boolean
  isLoading: boolean
  isLoadingWorkshops: boolean
  switchWorkshop: (workshopId: string | null) => Promise<void>
  workshops: Workshop[]
}

const WorkshopContext = createContext<WorkshopContextType | undefined>(undefined)

interface WorkshopProviderProps {
  children: React.ReactNode
  // initialWorkshopId: string | null
}

export function WorkshopProvider({ children }: WorkshopProviderProps) {
  const [currentWorkshop, setCurrentWorkshop] = useState<Workshop | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const { currentWorkshopId, setCurrentWorkshopId } = useRole();
  const [workshops, setWorkshops] = useState<Workshop[]>([])
  // Determine if user can toggle global view (only admins)
  const canToggleGlobalView = useIsAdmin()

  // Determine if we're in global view (admin with no workshop selected)
  const isGlobalView = canToggleGlobalView && !currentWorkshop

  // Fetch available workshops with 1 hour cache
  const { data: workshopsData = [], isLoading: isLoadingWorkshops } = useQuery({
    queryKey: USER_WORKSHOPS_AVAILABLE,
    queryFn: async () => {
      const response = await server.api.workshops.user.get({ query: { search: '' } })
      if (response.error) {
        throw new Error(response.error.value.message || 'Failed to fetch workshops')
      }
      if (currentWorkshopId){
        const workshop = response.data.data.find((w: Workshop) => w.id === currentWorkshopId)
        setCurrentWorkshop(workshop || null)
      }
      setWorkshops(response.data.data)
      return response.data.data
    },
    staleTime: 1000 * 60 * 60, // 1 hour cache
    gcTime: 1000 * 60 * 60, // Keep in cache for 1 hour
    // enabled: !!user // Only fetch when user is available
    // enabled: true
  })

  // Memoize workshops to prevent unnecessary re-renders
  const availableWorkshops = useMemo(() => workshopsData || [], [workshopsData])


  const switchWorkshop = async (workshopId: string | null) => {
    setIsLoading(true)
    try {
      const response = await server.api.workshops.switch.post({
        workshopId
      })
      console.log('switchWorkshop response:', response)
      
      if (response.error) {
        throw new Error(response.error.value.message || 'Failed to switch workshop')
      }

        if (workshopId === null && canToggleGlobalView) {
          // Global view
          setCurrentWorkshop(null)
        } else {
          // Find the workshop details from available workshops
          const workshop = availableWorkshops.find((w: Workshop) => w.id === workshopId)
          console.log('switchWorkshop workshop:', workshop)
          if (workshop) {
            setCurrentWorkshop(workshop)
            setCurrentWorkshopId(workshopId)
          }
        }
        
    } catch (error) {
      console.error('Error switching workshop:', error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  const value: WorkshopContextType = {
    currentWorkshop,
    availableWorkshops,
    isGlobalView,
    canToggleGlobalView,
    isLoading,
    isLoadingWorkshops,
    switchWorkshop,
    workshops
  }

  return (
    <WorkshopContext.Provider value={value}>
      {children}
    </WorkshopContext.Provider>
  )
}

export function useWorkshop() {
  const context = useContext(WorkshopContext)
  if (context === undefined) {
    throw new Error('useWorkshop must be used within a WorkshopProvider')
  }
  return context
}

// Hook to get current workshop ID for queries
export function useCurrentWorkshopId() {
  const { currentWorkshop, isGlobalView } = useWorkshop()
  
  // Return null for global view (admins only), workshop ID otherwise
  return isGlobalView ? null : currentWorkshop?.id || null
}
