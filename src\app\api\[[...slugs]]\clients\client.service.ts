import db from "@/config/db";
import { CreateClientInput, UpdateClientInput } from "./client.dto";
import { HttpException } from "@/lib/exceptions/HttpExceptions";

export class ClientService {

  static async create(data: CreateClientInput, createdById: string, workshopId: string) {
    // Check if a client with the same phone already exists
    const existingClient = await db.client.findFirst({
      where: { phone: data.phone }
    });

    if (existingClient) {
      throw HttpException.Conflict("A client with this phone number already exists");
    }

    // Check email if provided
    if (data.email) {
      const existingEmail = await db.client.findFirst({
        where: { email: data.email }
      });

      if (existingEmail) {
        throw HttpException.Conflict("A client with this email already exists");
      }
    }

    const client = await db.client.create({
      data: {
        ...data,
        createdById,
        workshopId
      },
      include: {
        vehicles: true,
        receptions: {
          include: {
            vehicle: true
          },
          orderBy: {
            createdAt: 'desc'
          }
        },
        _count: {
          select: {
            vehicles: true,
            receptions: true
          }
        }
      }
    });

    return client;
  }

  static async list(filters?: {
    clientType?: 'INDIVIDUAL' | 'FLEET';
    search?: string;
    workshopId?: string | null;
  }) {
    const where: any = {};

    // Filter by workshop - if null, show all (for admin global view)
    if (filters?.workshopId !== undefined) {
      if (filters.workshopId === null) {
        // Global view - no workshop filter
      } else {
        where.workshopId = filters.workshopId;
      }
    }

    if (filters?.clientType) {
      where.clientType = filters.clientType;
    }

    if (filters?.search) {
      where.OR = [
        { name: { contains: filters.search } },
        { phone: { contains: filters.search } },
        { email: { contains: filters.search } },
        { businessName: { contains: filters.search } }
      ];
    }

    return await db.client.findMany({
      where,
      include: {
        vehicles: true,
        _count: {
          select: {
            vehicles: true,
            receptions: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });
  }

  static async getById(id: string) {
    const client = await db.client.findUnique({
      where: { id },
      include: {
        vehicles: {
          include: {
            receptions: {
              include: {
                evidence: true
              },
              orderBy: {
                createdAt: 'desc'
              }
            }
          }
        },
        receptions: {
          include: {
            vehicle: true,
            evidence: true,
            receptionist: {
              select: {
                name: true,
                email: true
              }
            }
          },
          orderBy: {
            createdAt: 'desc'
          }
        },
        createdBy: {
          select: {
            name: true,
            email: true
          }
        }
      }
    });

    if (!client) {
      throw HttpException.NotFound("Client not found");
    }

    return client;
  }

  static async update(id: string, data: UpdateClientInput) {
    const existingClient = await db.client.findUnique({
      where: { id }
    });

    if (!existingClient) {
      throw HttpException.NotFound("Client not found");
    }

    // Check unique phone if updating
    if (data.phone && data.phone !== existingClient.phone) {
      const existingPhone = await db.client.findFirst({
        where: {
          phone: data.phone,
          id: { not: id }
        }
      });

      if (existingPhone) {
        throw HttpException.Conflict("A client with this phone number already exists");
      }
    }

    // Check unique email if updating
    if (data.email && data.email !== existingClient.email) {
      const existingEmail = await db.client.findFirst({
        where: {
          email: data.email,
          id: { not: id }
        }
      });

      if (existingEmail) {
        throw HttpException.Conflict("A client with this email already exists");
      }
    }

    return await db.client.update({
      where: { id },
      data,
      include: {
        vehicles: true,
        _count: {
          select: {
            vehicles: true,
            receptions: true
          }
        }
      }
    });
  }

  static async delete(id: string) {
    const client = await db.client.findUnique({
      where: { id },
      include: {
        receptions: true,
        vehicles: true
      }
    });

    if (!client) {
      throw HttpException.NotFound("Client not found");
    }

    if (client.receptions.length > 0) {
      throw HttpException.BadRequest("Cannot delete a client with registered receptions");
    }

    if (client.vehicles.length > 0) {
      throw HttpException.BadRequest("Cannot delete a client with registered vehicles");
    }

    return await db.client.delete({
      where: { id }
    });
  }

  static async getStats() {
    const [totalClients, individual, fleet, active] = await Promise.all([
      db.client.count(),
      db.client.count({ where: { clientType: 'INDIVIDUAL' } }),
      db.client.count({ where: { clientType: 'FLEET' } }),
      db.client.count({
        where: {
          receptions: {
            some: {
              status: {
                in: ['RECEIVED', 'IN_DIAGNOSTIC', 'WAITING_APPROVAL', 'IN_REPAIR']
              }
            }
          }
        }
      })
    ]);

    return {
      totalClients,
      individual,
      fleet,
      active
    };
  }
}
