'use client'

import { useRouter, useSearchParams } from 'next/navigation'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Search, Filter, X } from 'lucide-react'
import { useState, useEffect } from 'react'
import { Badge } from '../ui/badge'

const STATUS_OPTIONS = [
  { value: 'ALL', label: 'Todos los estados' },
  { value: 'PENDING', label: 'Pendiente' },
  { value: 'ACCEPTED', label: 'Aceptada' },
  { value: 'EXPIRED', label: 'Expirada' },
  { value: 'CANCELLED', label: 'Cancelada' }
]

const ROLE_OPTIONS = [
  { value: 'ALL', label: 'Todos los roles' },
  { value: 'ADMIN', label: 'Administrador' },
  { value: 'RECEPCIONISTA', label: 'Recepcion<PERSON>' },
  { value: 'TECNICO', label: 'Técnico' },
  { value: 'JEFE_TALLER', label: 'Jefe de Taller' },
  { value: 'COMPRAS', label: 'Compras' },
  { value: 'FACTURACION', label: 'Facturación' }
]

export function InvitationsFilters() {
  const router = useRouter()
  const searchParams = useSearchParams()
  
  const [search, setSearch] = useState('')
  const [status, setStatus] = useState('ALL')
  const [role, setRole] = useState('ALL')

  // Initialize filters from URL params after component mounts
  useEffect(() => {
    setSearch(searchParams.get('search') || '')
    setStatus(searchParams.get('status') || 'ALL')
    setRole(searchParams.get('role') || 'ALL')
  }, [])

  // Debounce search input
  useEffect(() => {
    const timer = setTimeout(() => {
      updateFilters({ search })
    }, 500)

    return () => clearTimeout(timer)
  }, [search])

  const updateFilters = (newFilters: { search?: string; status?: string; role?: string }) => {
    const params = new URLSearchParams(searchParams.toString())

    // Reset to page 1 when filters change
    params.set('page', '1')

    // Update filters
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value && value.trim() !== '' && value !== 'ALL') {
        params.set(key, value)
      } else {
        params.delete(key)
      }
    })

    router.push(`?${params.toString()}`)
  }

  const handleStatusChange = (newStatus: string) => {
    setStatus(newStatus)
    updateFilters({ status: newStatus })
  }

  const handleRoleChange = (newRole: string) => {
    setRole(newRole)
    updateFilters({ role: newRole })
  }

  const clearFilters = () => {
    setSearch('')
    setStatus('ALL')
    setRole('ALL')
    router.push('?page=1')
  }

  const hasActiveFilters = search || (status !== 'ALL') || (role !== 'ALL')

  return (
    <Card>
      <CardContent className="pt-6">
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div className="flex flex-col gap-4 md:flex-row md:items-center">
            {/* Search Input */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Buscar por email..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="pl-10 w-full md:w-64"
              />
            </div>

            {/* Status Filter */}
            <Select value={status} onValueChange={handleStatusChange}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="Estado" />
              </SelectTrigger>
              <SelectContent>
                {STATUS_OPTIONS.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* Role Filter */}
            <Select value={role} onValueChange={handleRoleChange}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="Rol" />
              </SelectTrigger>
              <SelectContent>
                {ROLE_OPTIONS.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Clear Filters Button */}
          {hasActiveFilters && (
            <Button variant="outline" onClick={clearFilters} className="w-full md:w-auto">
              <X className="h-4 w-4 mr-2" />
              Limpiar filtros
            </Button>
          )}
        </div>

        {/* Active Filters Display */}
        {hasActiveFilters && (
          <div className="flex flex-wrap gap-2 mt-4 pt-4 border-t">
            <span className="text-sm text-gray-600 flex items-center">
              <Filter className="h-4 w-4 mr-1" />
              Filtros activos:
            </span>
            {search && (
              <Badge variant="secondary" className="text-xs">
                Búsqueda: "{search}"
              </Badge>
            )}
            {status !== 'ALL' && (
              <Badge variant="secondary" className="text-xs">
                Estado: {STATUS_OPTIONS.find(opt => opt.value === status)?.label}
              </Badge>
            )}
            {role !== 'ALL' && (
              <Badge variant="secondary" className="text-xs">
                Rol: {ROLE_OPTIONS.find(opt => opt.value === role)?.label}
              </Badge>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
