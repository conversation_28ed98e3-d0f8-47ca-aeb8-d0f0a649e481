"use client"

import type React from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Sheet, Sheet<PERSON>ontent, SheetTrigger } from "@/components/ui/sheet"
import { <PERSON><PERSON>, Bell } from "lucide-react"
import { cn } from "@/lib/utils"
import { RoleSelector } from "@/components/role-selector"
import { WorkshopSwitcher } from "@/components/workshop-switcher"
import { getNavigationWithCurrent } from "@/lib/navigation"
import { useRole } from "@/hooks/use-role"
import { useState } from "react"
import { usePathname } from "next/navigation"
import Link from "next/link"
import UserMenu from "./user/user-menu"

interface DashboardLayoutProps {
  children: React.ReactNode
}

export function DashboardLayout({ children }: DashboardLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const { currentRole, setCurrentRole, user } = useRole();
  const pathname = usePathname()

  const navigation = getNavigationWithCurrent(currentRole, pathname)

  return (
    <div className="min-h-screen bg-background">
      {/* Mobile sidebar */}
      <Sheet open={sidebarOpen} onOpenChange={setSidebarOpen}>
        <SheetContent side="left" className="w-64 p-0">
          <Sidebar
            navigation={navigation}
          />
        </SheetContent>
      </Sheet>

      {/* Desktop sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-64 lg:flex-col">
        <Sidebar
          navigation={navigation}
        />
      </div>

      {/* Main content */}
      <div className="lg:pl-64">
        {/* Top navigation */}
        <div className="sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-border bg-card px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8">
          <Sheet>
            <SheetTrigger asChild>
              <Button variant="ghost" size="icon" className="lg:hidden">
                <Menu className="h-5 w-5" />
              </Button>
            </SheetTrigger>
          </Sheet>

          <div className="flex flex-1 gap-x-4 self-stretch lg:gap-x-6">
            <div className="flex flex-1"></div>
            <div className="flex items-center gap-x-4 lg:gap-x-6">
              {/* <HydrationBoundary
                fallback={
                  <div className="flex items-center gap-x-4 lg:gap-x-6">
                    <div className="h-8 w-24 bg-muted animate-pulse rounded"></div>
                    <div className="h-8 w-8 bg-muted animate-pulse rounded"></div>
                    <div className="h-8 w-8 bg-muted animate-pulse rounded-full"></div>
                  </div>
                }
              > */}
                <RoleSelector
                  currentRole={currentRole}
                  onRoleChange={setCurrentRole}
                  showBadge={true}
                />
                <Button variant="ghost" size="icon">
                  <Bell className="h-5 w-5" />
                </Button>

                {/* User Menu */}
               <UserMenu />
              {/* </HydrationBoundary> */}
            </div>
          </div>
        </div>

        {/* Page content */}
        <main className="py-6">
          <div className="px-4 sm:px-6 lg:px-8">{children}</div>
        </main>
      </div>
    </div>
  )
}

interface SidebarProps {
  navigation: any[]
}

function Sidebar({
  navigation,
}: Omit<SidebarProps, 'onRoleChange'>) {
  return (
    <div className="flex grow flex-col gap-y-5 overflow-y-auto overflow-x-hidden bg-card border-r border-border px-6 pb-4 w-full">
      <div className="flex h-16 shrink-0 items-center">
        <Link href="/dashboard" className="flex items-center gap-2">
          <div className="h-8 w-8 rounded-lg bg-primary flex items-center justify-center">
            <span className="text-primary-foreground font-bold text-sm">R</span>
          </div>
          <span className="text-lg font-bold text-foreground">Ruedda</span>
        </Link>
      </div>

      <div className="border-b border-border pb-4">
        <WorkshopSwitcher />
      </div>

      <nav className="flex flex-1 flex-col">
        <ul role="list" className="flex flex-1 flex-col gap-y-7">
          <li>
            <ul role="list" className="-mx-2 space-y-1">
              {navigation.map((item) => (
                <li key={item.name}>
                  <Link
                    href={item.href}
                    className={cn(
                      item.current
                        ? "bg-primary/10 text-primary border-r-2 border-primary"
                        : "text-muted-foreground hover:bg-muted hover:text-foreground",
                      "group flex gap-x-3 rounded-l-md p-2 text-sm font-medium leading-6 transition-colors",
                    )}
                  >
                    {item.icon && <item.icon className="h-5 w-5 shrink-0" />}
                    {item.name}
                  </Link>
                </li>
              ))}
            </ul>
          </li>
        </ul>
      </nav>
    </div>
  )
}
