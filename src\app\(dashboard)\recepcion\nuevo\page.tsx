'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Car, AlertCircle, Search, User, Camera, Upload } from 'lucide-react'
import Link from 'next/link'
import { DashboardHeader } from '@/components/ui/dashboard-header'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { server } from '@/app/api/server'
import { UploadDropzone } from '@/lib/uploadthing'
import toast from 'react-hot-toast'

// Tipos
type EvidenceType = 'VIN' | 'DASHBOARD' | 'FRONT' | 'LEFT_FRONT_SIDE' | 'DRIVER_SEAT' | 'LEFT_REAR_SIDE' | 'LEFT_REAR_SEAT' | 'REAR' | 'TRUNK' | 'SPARE_TIRE' | 'RIGHT_REAR_SIDE' | 'RIGHT_REAR_SEAT' | 'RIGHT_FRONT_SIDE' | 'PASSENGER_SEAT' | 'EXTERIOR_DAMAGE' | 'ENGINE_BAY'

type Evidence = {
  type: EvidenceType
  url: string
  description?: string
}

// Schema de validación
const receptionSchema = z.object({
  clientId: z.string().min(1, 'Debe seleccionar un cliente'),
  vehicleId: z.string().min(1, 'Debe seleccionar un vehículo'),
  mileage: z.number().min(0, 'El kilometraje debe ser mayor a 0'),
  serviceType: z.enum(['MINOR_PREVENTIVE', 'MAJOR_PREVENTIVE', 'SPARK_PLUG_CHANGE', 'DIAGNOSTIC', 'BODYWORK_PAINT', 'OTHER']),
  otherService: z.string().optional(),
  observations: z.string().optional(),
  tiresCondition: z.enum(['GOOD_APPEARANCE', 'SOME_DETAILS']).optional(),
  assignedTech: z.string().optional(),
  evidence: z.array(z.object({
    type: z.enum(['VIN', 'DASHBOARD', 'FRONT', 'LEFT_FRONT_SIDE', 'DRIVER_SEAT', 'LEFT_REAR_SIDE', 'LEFT_REAR_SEAT', 'REAR', 'TRUNK', 'SPARE_TIRE', 'RIGHT_REAR_SIDE', 'RIGHT_REAR_SEAT', 'RIGHT_FRONT_SIDE', 'PASSENGER_SEAT', 'EXTERIOR_DAMAGE', 'ENGINE_BAY']),
    url: z.string(),
    description: z.string().optional()
  })).default([])
})

type ReceptionFormData = z.infer<typeof receptionSchema>

const SERVICE_TYPES = [
  { value: 'MINOR_PREVENTIVE', label: 'Servicio Preventivo Menor' },
  { value: 'MAJOR_PREVENTIVE', label: 'Servicio Preventivo Mayor' },
  { value: 'SPARK_PLUG_CHANGE', label: 'Cambio de Bujías' },
  { value: 'DIAGNOSTIC', label: 'Diagnóstico' },
  { value: 'BODYWORK_PAINT', label: 'Hojalatería y Pintura' },
  { value: 'OTHER', label: 'Otro' }
]

const EVIDENCE_TYPES: Array<{ value: EvidenceType, label: string, required: boolean, maxFiles: number }> = [
  { value: 'VIN', label: 'Foto de VIN', required: true, maxFiles: 1 },
  { value: 'DASHBOARD', label: 'Foto del tablero', required: true, maxFiles: 5 },
  { value: 'FRONT', label: 'Foto de parte frontal', required: true, maxFiles: 5 },
  { value: 'LEFT_FRONT_SIDE', label: 'Foto lateral izquierdo (parte delantera)', required: true, maxFiles: 5 },
  { value: 'DRIVER_SEAT', label: 'Foto de asiento de piloto', required: true, maxFiles: 5 },
  { value: 'LEFT_REAR_SIDE', label: 'Foto lateral izquierdo (parte trasera)', required: true, maxFiles: 5 },
  { value: 'LEFT_REAR_SEAT', label: 'Foto de asiento trasero izquierdo', required: false, maxFiles: 5 },
  { value: 'REAR', label: 'Foto trasera', required: false, maxFiles: 5 },
  { value: 'TRUNK', label: 'Foto de cajuela', required: false, maxFiles: 5 },
  { value: 'SPARE_TIRE', label: 'Foto de llanta de refacción', required: false, maxFiles: 5 },
  { value: 'RIGHT_REAR_SIDE', label: 'Foto lateral derecho (parte trasera)', required: false, maxFiles: 5 },
  { value: 'RIGHT_REAR_SEAT', label: 'Foto de asiento trasero derecho', required: false, maxFiles: 5 },
  { value: 'RIGHT_FRONT_SIDE', label: 'Foto lateral derecho (parte delantera)', required: false, maxFiles: 5 },
  { value: 'PASSENGER_SEAT', label: 'Foto de asiento de copiloto', required: false, maxFiles: 5 },
  { value: 'EXTERIOR_DAMAGE', label: 'Foto de daños exteriores', required: false, maxFiles: 5 },
  { value: 'ENGINE_BAY', label: 'Foto de bahía del motor', required: false, maxFiles: 5 }
]

export default function NewReceptionPage() {
  const router = useRouter()
  const queryClient = useQueryClient()
  const [clientSearch, setClientSearch] = useState('')
  const [selectedClient, setSelectedClient] = useState<any>(null)
  const [selectedVehicle, setSelectedVehicle] = useState<any>(null)
  const [evidence, setEvidence] = useState<Evidence[]>([])

  const form = useForm<ReceptionFormData>({
    resolver: zodResolver(receptionSchema),
    defaultValues: {
      clientId: '',
      vehicleId: '',
      mileage: 0,
      serviceType: 'MINOR_PREVENTIVE',
      otherService: '',
      observations: '',
      tiresCondition: 'GOOD_APPEARANCE',
      assignedTech: '',
      evidence: []
    }
  })

  // Query para buscar clientes
  const { data: clientsData } = useQuery({
    queryKey: ['clientes', { search: clientSearch }],
    queryFn: async () => {
      if (!clientSearch || clientSearch.length < 2) return { success: true, data: [] }
      const response = await server.api.clients.get({
        query: { search: clientSearch }
      })
      return response.data
    },
    enabled: clientSearch.length >= 2
  })

  // Query para obtener vehículos del cliente seleccionado
  const { data: vehiclesData } = useQuery({
    queryKey: ['vehicles', { clientId: selectedClient?.id }],
    queryFn: async () => {
      if (!selectedClient?.id) return { success: true, data: [] }
      const response = await server.api.vehicles.get({
        query: { clientId: selectedClient.id }
      })
      return response.data
    },
    enabled: !!selectedClient?.id
  })

  const clients = clientsData?.success ? clientsData.data || [] : []
  const vehicles = vehiclesData?.success ? vehiclesData.data || [] : []

  // Mutation para crear recepción
  const createReceptionMutation = useMutation({
    mutationFn: async (data: ReceptionFormData) => {
      const response = await server.api.receptions.post(data)
      return response.data
    },
    onSuccess: (data) => {
      console.log('Respuesta del servidor:', data) // Para debug
      if (data?.success && data?.data?.id) {
        toast.success('Recepción registrada exitosamente')
        queryClient.invalidateQueries({ queryKey: ['recepciones'] })
        router.push(`/recepcion/${data.data.id}`)
      } else if (data?.success) {
        // Si hay éxito pero no hay ID, redirigir a la lista
        toast.success('Recepción registrada exitosamente')
        queryClient.invalidateQueries({ queryKey: ['recepciones'] })
        router.push('/recepcion')
      }
    },
    onError: (error) => {
      toast.error('Error al registrar la recepción')
      console.error('Error en mutation:', error)
    }
  })

  const onSubmit = (data: ReceptionFormData) => {
    // Si no hay evidencias, generar URLs aleatorias para la demo
    if (!evidence || evidence.length === 0) {
      const evidenceTypes: EvidenceType[] =
        ['VIN', 'DASHBOARD', 'FRONT', 'LEFT_FRONT_SIDE', 'DRIVER_SEAT', 'REAR']
      data.evidence = evidenceTypes.map(type => ({
        type,
        url: `https://picsum.photos/800/600?random=${Math.floor(Math.random() * 1000)}`,
        description: `Evidencia ${type.toLowerCase().replace('_', ' ')}`
      }))
    } else {
      data.evidence = evidence
    }

    console.log('Datos a enviar:', data)
    createReceptionMutation.mutate(data)
  }

  const handleClientSelect = (client: any) => {
    setSelectedClient(client)
    setSelectedVehicle(null)
    setClientSearch('') // Limpiar el input de búsqueda
    form.setValue('clientId', client.id)
    form.setValue('vehicleId', '')
    form.clearErrors('clientId')
  }

  const handleVehicleSelect = (vehicle: any) => {
    setSelectedVehicle(vehicle)
    form.setValue('vehicleId', vehicle.id)
    form.clearErrors('vehicleId')
  }

  const handleEvidenceUpload = (type: EvidenceType, urls: string[]) => {
    const newEvidence: Evidence[] = urls.map(url => ({ type, url }))
    setEvidence(prev => [...prev.filter(e => e.type !== type), ...newEvidence])
    form.setValue('evidence', [...evidence.filter(e => e.type !== type), ...newEvidence])
  }

  return (
    <div className="p-6">
      <div className="max-w-6xl mx-auto">
        <DashboardHeader
          title="Nueva Recepción de Vehículo"
          description="Registra la recepción física de un vehículo en el taller"
          backUrl="/recepcion"
        />

        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Selección de Cliente y Vehículo */}
          <Card>
            <CardHeader>
              <CardTitle>Cliente y Vehículo</CardTitle>
              <CardDescription>
                Selecciona el cliente y el vehículo que se está recibiendo
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Cliente */}
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="clientSearch">Buscar Cliente *</Label>
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                      <Input
                        id="clientSearch"
                        placeholder="Buscar por nombre, teléfono..."
                        value={clientSearch}
                        onChange={(e) => setClientSearch(e.target.value)}
                        className="pl-10"
                      />
                    </div>
                    {form.formState.errors.clientId && (
                      <p className="text-sm text-red-600">{form.formState.errors.clientId.message}</p>
                    )}
                  </div>

                  {clients.length > 0 && !selectedClient && (
                    <div className="border rounded-lg max-h-40 overflow-y-auto">
                      {clients.map((client: any) => (
                        <div
                          key={client.id}
                          className="p-3 border-b last:border-b-0 cursor-pointer hover:bg-gray-50"
                          onClick={() => handleClientSelect(client)}
                        >
                          <div className="font-medium">{client.name}</div>
                          <div className="text-sm text-gray-500">{client.phone}</div>
                        </div>
                      ))}
                    </div>
                  )}

                  {selectedClient && (
                    <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="font-medium text-green-800">Cliente: {selectedClient.name}</div>
                          <div className="text-sm text-green-600">{selectedClient.phone}</div>
                        </div>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setSelectedClient(null)
                            setSelectedVehicle(null)
                            setClientSearch('')
                            form.setValue('clientId', '')
                            form.setValue('vehicleId', '')
                          }}
                        >
                          Cambiar
                        </Button>
                      </div>
                    </div>
                  )}
                </div>

                {/* Vehículo */}
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label>Seleccionar Vehículo *</Label>
                    {form.formState.errors.vehicleId && (
                      <p className="text-sm text-red-600">{form.formState.errors.vehicleId.message}</p>
                    )}
                  </div>

                  {selectedClient ? (
                    vehicles.length > 0 ? (
                      <div className="space-y-2">
                        {vehicles.map((vehicle: any) => (
                          <div
                            key={vehicle.id}
                            className={`p-3 border rounded-lg cursor-pointer hover:bg-gray-50 ${
                              selectedVehicle?.id === vehicle.id ? 'bg-blue-50 border-blue-200' : ''
                            }`}
                            onClick={() => handleVehicleSelect(vehicle)}
                          >
                            <div className="font-medium">
                              {vehicle.brand} {vehicle.model} {vehicle.year}
                            </div>
                            <div className="text-sm text-gray-500">
                              Placas: {vehicle.plates}
                              {vehicle.color && ` • Color: ${vehicle.color}`}
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-4 text-gray-500">
                        Este cliente no tiene vehículos registrados.
                        <br />
                        <Link href="/vehiculos/nuevo" className="text-blue-600 hover:underline">
                          Registrar vehículo
                        </Link>
                      </div>
                    )
                  ) : (
                    <div className="text-center py-4 text-gray-500">
                      Primero selecciona un cliente
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Información del Servicio */}
          <Card>
            <CardHeader>
              <CardTitle>Información del Servicio</CardTitle>
              <CardDescription>
                Detalles del servicio solicitado
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="kilometraje">Kilometraje *</Label>
                  <Input
                    id="mileage"
                    type="number"
                    {...form.register('mileage', { valueAsNumber: true })}
                    placeholder="Kilometraje actual"
                    min="0"
                  />
                  {form.formState.errors.mileage && (
                    <p className="text-sm text-red-600">{form.formState.errors.mileage.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="serviceType">Tipo de Servicio *</Label>
                  <select
                    id="serviceType"
                    {...form.register('serviceType')}
                    className="flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50"
                  >
                    {SERVICE_TYPES.map((type) => (
                      <option key={type.value} value={type.value}>
                        {type.label}
                      </option>
                    ))}
                  </select>
                  {form.formState.errors.serviceType && (
                    <p className="text-sm text-red-600">{form.formState.errors.serviceType.message}</p>
                  )}
                </div>

                {form.watch('serviceType') === 'OTHER' && (
                  <div className="space-y-2 md:col-span-2">
                    <Label htmlFor="otherService">Especificar Otro Servicio</Label>
                    <Input
                      id="otherService"
                      {...form.register('otherService')}
                      placeholder="Describe el servicio solicitado"
                    />
                  </div>
                )}

                <div className="space-y-2">
                  <Label htmlFor="tiresCondition">Estado de las Llantas</Label>
                  <select
                    id="tiresCondition"
                    {...form.register('tiresCondition')}
                    className="flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50"
                  >
                    <option value="GOOD_APPEARANCE">Buena apariencia</option>
                    <option value="SOME_DETAIL">Detalle en algunas</option>
                  </select>
                </div>

                <div className="space-y-2 md:col-span-2">
                  <Label htmlFor="observations">Observaciones</Label>
                  <textarea
                    id="observations"
                    {...form.register('observations')}
                    placeholder="Observaciones adicionales del cliente o del vehículo"
                    className="flex min-h-[80px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Evidencias Fotográficas */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Camera className="w-5 h-5" />
                Evidencias Fotográficas (Opcional)
              </CardTitle>
              <CardDescription>
                Sube fotos para documentar el estado del vehículo. Si no subes fotos, se generarán automáticamente para la demo.
              </CardDescription>
            </CardHeader>
            <CardContent>
              {form.formState.errors.evidence && (
                <Alert variant="destructive" className="mb-4">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{form.formState.errors.evidence.message}</AlertDescription>
                </Alert>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {EVIDENCE_TYPES.filter(type => type.required).map((evidenceType) => (
                  <div key={evidenceType.value} className="space-y-3">
                    <Label className="text-sm font-medium">
                      {evidenceType.label} {evidenceType.required && '*'}
                    </Label>
                    <div className="border-2 border-dashed border-gray-300 rounded-lg p-4">
                      <UploadDropzone
                        endpoint="evidenciaFoto"
                        onClientUploadComplete={(res) => {
                          const urls = res?.map(file => file.url) || []
                          handleEvidenceUpload(evidenceType.value, urls)
                          toast.success('Fotos subidas exitosamente')
                        }}
                        onUploadError={(error: Error) => {
                          toast.error(`Error: ${error.message}`)
                        }}
                        config={{
                          mode: "auto",
                        }}
                      />
                    </div>
                    {evidence.filter(e => e.type === evidenceType.value).length > 0 && (
                      <div className="text-sm text-green-600">
                        ✓ {evidence.filter(e => e.type === evidenceType.value).length} foto(s) subida(s)
                      </div>
                    )}
                  </div>
                ))}
              </div>

              <div className="mt-6">
                <h4 className="font-medium mb-3">Fotos Opcionales</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {EVIDENCE_TYPES.filter(type => !type.required).map((evidenceType) => (
                    <div key={evidenceType.value} className="space-y-2">
                      <Label className="text-sm">{evidenceType.label}</Label>
                      <div className="border border-gray-200 rounded p-2">
                        <UploadDropzone
                          endpoint="evidenciaFoto"
                          onClientUploadComplete={(res) => {
                            const urls = res?.map(file => file.url) || []
                            handleEvidenceUpload(evidenceType.value, urls)
                            toast.success('Fotos subidas')
                          }}
                          onUploadError={(error: Error) => {
                            toast.error(`Error: ${error.message}`)
                          }}
                          config={{
                            mode: "auto",
                          }}
                        />
                      </div>
                      {evidence.filter(e => e.type === evidenceType.value).length > 0 && (
                        <div className="text-xs text-green-600">
                          ✓ {evidence.filter(e => e.type === evidenceType.value).length} foto(s)
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Botones */}
          <div className="flex gap-4">
            <Button
              type="submit"
              disabled={createReceptionMutation.isPending}
              className="flex-1 md:flex-none"
              size="lg"
            >
              {createReceptionMutation.isPending ? 'Registrando...' : 'Registrar Recepción'}
            </Button>
            <Button type="button" variant="outline" asChild size="lg">
              <Link href="/recepcion">Cancelar</Link>
            </Button>
          </div>
        </form>
      </div>
    </div>
  )
}
