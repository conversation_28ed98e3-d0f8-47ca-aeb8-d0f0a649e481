// User types adapted from example project to work with current Prisma schema

export enum UserRole {
  ADMIN = "ADMIN",
  RECEPCIONISTA = "RECEPCIONISTA", 
  TECNICO = "TECNICO",
  JEFE_TALLER = "JEFE_TALLER",
  COMPRAS = "COMPRAS",
  FACTURACION = "FACTURACION",
}

// Workshop interface for multi-location support (future enhancement)
export interface Workshop {
  id: string
  name: string
  location: string
  isActive: boolean
}

// User interface matching current Prisma schema
export interface User {
  id: string
  name: string
  email: string
  emailVerified: boolean
  image?: string
  role: UserRole
  isActive: boolean
  createdAt: Date
  updatedAt: Date
  
  // For future multi-workshop support
  workshops?: Workshop[]
  currentWorkshop?: Workshop
}

// User creation data
export interface CreateUserData {
  name: string
  email: string
  role: UserRole
  image?: string
}

// User update data
export interface UpdateUserData {
  name?: string
  role?: UserRole
  isActive?: boolean
  image?: string
}
