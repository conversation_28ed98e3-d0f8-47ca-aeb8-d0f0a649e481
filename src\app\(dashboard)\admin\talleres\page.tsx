"use client"

import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { useIsAdmin } from '@/hooks/use-role'
import { server } from '@/app/api/server'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { DashboardHeader } from '@/components/ui/dashboard-header'
import { 
  Building2, 
  Plus, 
  Search, 
  MapPin, 
  Phone, 
  Mail,
  Settings,
  Users,
  Edit,
  Trash2,
  Shield,
  Loader2
} from 'lucide-react'
import Link from 'next/link'

interface Workshop {
  id: string
  name: string
  location: string
  phone?: string
  email?: string
  isActive: boolean
  userCount: number
  createdAt: string
}

export default function WorkshopsPage() {
  const isAdmin = useIsAdmin()
  const [search, setSearch] = useState('')

  // Redirect if not admin
  if (!isAdmin) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5 text-destructive" />
              Acceso Denegado
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-4">
              No tienes permisos para gestionar talleres.
            </p>
            <Button asChild>
              <Link href="/admin">
                Volver al Panel de Admin
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Fetch workshops from server
  const { data: workshopsData, isLoading: workshopsLoading, refetch } = useQuery({
    queryKey: ['admin-workshops'],
    queryFn: async () => {
      const response = await server.api.workshops.get()
      if (!response.data?.success) {
        throw new Error(response.data?.error || 'Failed to fetch workshops')
      }
      return response.data.data as Workshop[]
    }
  })

  const workshops = workshopsData || []

  if (workshopsLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto mb-4" />
          <p className="text-muted-foreground">Cargando talleres...</p>
        </div>
      </div>
    )
  }

  const filteredWorkshops = workshops.filter(workshop =>
    workshop.name.toLowerCase().includes(search.toLowerCase()) ||
    workshop.location.toLowerCase().includes(search.toLowerCase())
  )

  return (
    <div className="p-6">
      <div className="max-w-7xl mx-auto">
        <div className="flex items-center justify-between mb-8">
          <DashboardHeader
            title="Gestión de Talleres"
            description="Administra talleres, configuraciones y ubicaciones"
            showBackButton={true}
            backButtonHref="/admin"
          />
          <Button asChild size="lg">
            <Link href="/admin/talleres/nuevo">
              <Plus className="w-5 h-5 mr-2" />
              Nuevo Taller
            </Link>
          </Button>
        </div>

        {/* Search and Filters */}
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="flex items-center gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="Buscar talleres por nombre o ubicación..."
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Workshops List */}
        {filteredWorkshops.length === 0 ? (
          <Card>
            <CardContent className="text-center py-12">
              <Building2 className="h-16 w-16 mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {search ? 'No se encontraron talleres' : 'No hay talleres registrados'}
              </h3>
              <p className="text-gray-500 mb-6">
                {search 
                  ? 'Intenta con otros términos de búsqueda'
                  : 'Comienza creando tu primer taller'
                }
              </p>
              {!search && (
                <Button asChild>
                  <Link href="/admin/talleres/nuevo">
                    <Plus className="w-4 h-4 mr-2" />
                    Crear Primer Taller
                  </Link>
                </Button>
              )}
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {filteredWorkshops.map((workshop) => (
              <Card key={workshop.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-3">
                      <Building2 className="h-8 w-8 text-primary" />
                      <div>
                        <CardTitle className="text-lg">{workshop.name}</CardTitle>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge variant={workshop.isActive ? "default" : "secondary"}>
                            {workshop.isActive ? "Activo" : "Inactivo"}
                          </Badge>
                          <Badge variant="outline" className="text-xs">
                            <Users className="w-3 h-3 mr-1" />
                            {workshop.userCount} usuarios
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex items-start gap-2">
                      <MapPin className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />
                      <span className="text-sm text-muted-foreground">{workshop.location}</span>
                    </div>
                    {workshop.phone && (
                      <div className="flex items-center gap-2">
                        <Phone className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm text-muted-foreground">{workshop.phone}</span>
                      </div>
                    )}
                    {workshop.email && (
                      <div className="flex items-center gap-2">
                        <Mail className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm text-muted-foreground">{workshop.email}</span>
                      </div>
                    )}
                  </div>

                  <div className="flex gap-2 pt-4 border-t">
                    <Button asChild size="sm" variant="outline" className="flex-1">
                      <Link href={`/admin/talleres/${workshop.id}`}>
                        <Edit className="w-4 h-4 mr-1" />
                        Editar
                      </Link>
                    </Button>
                    <Button asChild size="sm" variant="outline" className="flex-1">
                      <Link href={`/admin/talleres/${workshop.id}/configuracion`}>
                        <Settings className="w-4 h-4 mr-1" />
                        Config
                      </Link>
                    </Button>
                    <Button asChild size="sm" variant="outline" className="flex-1">
                      <Link href={`/admin/talleres/${workshop.id}/usuarios`}>
                        <Users className="w-4 h-4 mr-1" />
                        Usuarios
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Summary Stats */}
        {filteredWorkshops.length > 0 && (
          <Card className="mt-8">
            <CardHeader>
              <CardTitle>Resumen</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary">{workshops.length}</div>
                  <p className="text-sm text-muted-foreground">Total Talleres</p>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {workshops.filter(w => w.isActive).length}
                  </div>
                  <p className="text-sm text-muted-foreground">Talleres Activos</p>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {workshops.reduce((sum, w) => sum + w.userCount, 0)}
                  </div>
                  <p className="text-sm text-muted-foreground">Total Usuarios</p>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-600">
                    {workshops.filter(w => !w.isActive).length}
                  </div>
                  <p className="text-sm text-muted-foreground">Talleres Inactivos</p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
