'use client';

import { But<PERSON> } from '@/components/ui/button';
import { authClient } from '@/lib/auth-client';
import { useRouter } from 'next/navigation';
import React from 'react'

export default function SignOutButton() {
  const router = useRouter();

  const handleSignOut = async () => {
    await authClient.signOut();
    router.push('/login');
  };

  return (
    <Button
      onClick={handleSignOut}
      variant="outline"
    >
      Cerrar <PERSON>sión
    </Button>
  )
}
