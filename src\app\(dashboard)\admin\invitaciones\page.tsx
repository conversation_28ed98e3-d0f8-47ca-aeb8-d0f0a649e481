import { UserPlus } from 'lucide-react'
import { CreateInvitationModal } from '@/components/invitations/create-invitation-modal'
import { InvitationsFilters } from '@/components/invitations/invitations-filters'
import { InvitationsTableWrapper } from '@/components/invitations/invitations-table-wrapper'

interface InvitationsPageProps {
  searchParams: Promise<{
    page?: string
    status?: string
    role?: string
    search?: string
  }>
}

export default async function InvitationsPage({ searchParams: searchParams2 }: InvitationsPageProps) {
  const searchParams = await searchParams2;
  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <UserPlus className="h-8 w-8" />
              <div>
                <h1 className="text-3xl font-bold text-gray-900">
                  Gestionar Invitaciones
                </h1>
                <p className="text-gray-600 mt-1">
                  Invitar nuevos usuarios al sistema
                </p>
              </div>
            </div>
            <CreateInvitationModal />
          </div>
        </div>

        {/* Filters */}
        <div className="mb-6">
          <InvitationsFilters />
        </div>

        {/* Invitations Table */}
        <InvitationsTableWrapper searchParams={searchParams} />
      </div>
    </div>
  )
}
