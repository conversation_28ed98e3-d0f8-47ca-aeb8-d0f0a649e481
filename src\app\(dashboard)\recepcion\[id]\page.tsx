'use client'

import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  FileText, 
  ArrowLeft, 
  Edit, 
  User, 
  Car, 
  Phone,
  Calendar,
  Wrench,
  Eye,
  Camera,
  Download
} from 'lucide-react'
import Link from 'next/link'
import { useQuery } from '@tanstack/react-query'
import Image from 'next/image'
import { server } from '@/app/api/server'
import { DashboardHeader } from '@/components/ui/dashboard-header'
import { getServiceTypeLabel, getStatusLabel, getTiresConditionLabel, getEvidenceTypeLabel } from '@/lib/mappers/reception-mappers'

export default function ReceptionDetailPage() {
  const params = useParams()
  const router = useRouter()
  const receptionId = params.id as string

  const { data: reception, isLoading } = useQuery({
    queryKey: ['reception', receptionId],
    queryFn: async () => {
      const response = await server.api.receptions({ id: receptionId }).get();
      // return response.data
      if (response.error) {
        throw new Error(response.error.value.message)
      }
      return response.data.data;
    }
  })


  if (isLoading) {
    return (
      <div className="p-6">
        <div className="max-w-6xl mx-auto">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-gray-200 rounded w-1/3"></div>
            <div className="h-64 bg-gray-200 rounded"></div>
            <div className="h-48 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    )
  }

  if (!reception) {
    return (
      <div className="p-6">
        <div className="max-w-6xl mx-auto">
          <div className="text-center py-12">
            <FileText className="h-16 w-16 mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Recepción no encontrada
            </h3>
            <p className="text-gray-500 mb-6">
              La recepción que buscas no existe o ha sido eliminada
            </p>
            <Button asChild>
              <Link href="/recepcion">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Volver a Recepciones
              </Link>
            </Button>
          </div>
        </div>
      </div>
    )
  }

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      'RECEIVED': { color: 'bg-blue-100 text-blue-800', label: 'Recibido' },
      'IN_DIAGNOSTIC': { color: 'bg-yellow-100 text-yellow-800', label: 'En Diagnóstico' },
      'WAITING_APPROVAL': { color: 'bg-orange-100 text-orange-800', label: 'Esperando Aprobación' },
      'WAITING_PARTS': { color: 'bg-purple-100 text-purple-800', label: 'Esperando Refacciones' },
      'IN_REPAIR': { color: 'bg-indigo-100 text-indigo-800', label: 'En Reparación' },
      'COMPLETED': { color: 'bg-green-100 text-green-800', label: 'Terminado' },
      'DELIVERED': { color: 'bg-gray-100 text-gray-800', label: 'Entregado' }
    }

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.RECEIVED

    return (
      <Badge className={config.color}>
        {config.label}
      </Badge>
    )
  }

  const formatFecha = (fecha: string | Date) => {
    return new Date(fecha).toLocaleDateString('es-MX', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <div className="p-6">
      <div className="max-w-6xl mx-auto">
        <div className="flex items-center justify-between mb-8">
          <DashboardHeader
            title={`Recepción #${reception.id.slice(-8).toUpperCase()}`}
            description={`${reception.vehicle.brand} ${reception.vehicle.model} ${reception.vehicle.year} - ${getStatusLabel(reception.status)}`}
            backUrl="/reception"
          />
          <div className="flex gap-2">
            <Button asChild>
              <Link href={`/reception/${reception.id}/editar`}>
                <Edit className="w-4 h-4 mr-2" />
                Editar
              </Link>
            </Button>
            <Button variant="outline">
              <Download className="w-4 h-4 mr-2" />
              Imprimir
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Información Principal */}
          <div className="lg:col-span-2 space-y-6">
            {/* Información del Servicio */}
            <Card>
              <CardHeader>
                <CardTitle>Información del Servicio</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <div className="text-sm text-gray-500">Tipo de Servicio</div>
                    <div className="font-medium">
                      {getServiceTypeLabel(reception.serviceType)}
                      {reception.otherService && ` - ${reception.otherService}`}
                    </div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500">Kilometraje</div>
                    <div className="font-medium">{reception.mileage?.toLocaleString()} km</div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500">Fecha de Entrada</div>
                    <div className="font-medium">{formatFecha(reception.entryDate)}</div>
                  </div>

                  {reception.tiresCondition && (
                    <div>
                      <div className="text-sm text-gray-500">Estado de Llantas</div>
                      <div className="font-medium">{getTiresConditionLabel(reception.tiresCondition)}</div>
                    </div>
                  )}
                  {reception.assignedTech && (
                    <div>
                      <div className="text-sm text-gray-500">Técnico Asignado</div>
                      <div className="font-medium">{reception.assignedTech}</div>
                    </div>
                  )}
                </div>
                
                {reception.observations && (
                  <div className="mt-4 pt-4 border-t">
                    <div className="text-sm text-gray-500 mb-2">Observaciones</div>
                    <div className="text-gray-900">{reception.observations}</div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Información del Vehículo y Cliente */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Vehículo</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Car className="w-8 h-8 text-gray-400" />
                      <div>
                        <div className="font-medium">
                          {reception.vehicle.brand} {reception.vehicle.model}
                        </div>
                        <div className="text-sm text-gray-500">
                          {reception.vehicle.year} • {reception.vehicle.plates}
                        </div>
                        {reception.vehicle.color && (
                          <div className="text-sm text-gray-500">
                            Color: {reception.vehicle.color}
                          </div>
                        )}
                      </div>
                    </div>
                    <Button asChild size="sm" variant="outline">
                      <Link href={`/vehiculos/${reception.vehicle.id}`}>
                        <Eye className="w-4 h-4 mr-2" />
                        Ver
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Cliente</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <User className="w-8 h-8 text-gray-400" />
                      <div>
                        <div className="font-medium">{reception.client.name}</div>
                        <div className="flex items-center gap-1 text-sm text-gray-500">
                          <Phone className="w-3 h-3" />
                          {reception.client.phone}
                        </div>
                        {reception.client.clientType === 'FLEET' && (
                          <Badge variant="secondary" className="mt-1 text-xs">
                            Flotillero
                          </Badge>
                        )}
                      </div>
                    </div>
                    <Button asChild size="sm" variant="outline">
                      <Link href={`/clientes/${reception.client.id}`}>
                        <Eye className="w-4 h-4 mr-2" />
                        Ver
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Evidencias Fotográficas */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Camera className="w-5 h-5" />
                  Evidencias Fotográficas
                </CardTitle>
              </CardHeader>
              <CardContent>
                {reception.evidence && reception.evidence.length > 0 ? (
                  <div className="space-y-6">
                    {/* Agrupar evidencias por tipo */}
                    {Object.entries(
                      reception.evidence.reduce((acc: any, evidence: any) => {
                        if (!acc[evidence.type]) acc[evidence.type] = []
                        acc[evidence.type].push(evidence)
                        return acc
                      }, {})
                    ).map(([type, evidences]: [string, any]) => (
                      <div key={type}>
                        <h4 className="font-medium mb-3 text-gray-900">
                          {getEvidenceTypeLabel(type)}
                        </h4>
                        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                          {evidences.map((evidence: any, index: number) => (
                            <div key={index} className="relative group">
                              <div className="aspect-square rounded-lg overflow-hidden bg-gray-100">
                                <Image
                                  src={evidence.url}
                                  alt={`${getEvidenceTypeLabel(type)} ${index + 1}`}
                                  width={200}
                                  height={200}
                                  className="w-full h-full object-cover group-hover:scale-105 transition-transform"
                                />
                              </div>
                              {evidence.description && (
                                <p className="text-xs text-gray-500 mt-1 truncate">
                                  {evidence.description}
                                </p>
                              )}
                            </div>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <Camera className="h-12 w-12 mx-auto mb-3 text-gray-300" />
                    <p>No hay evidencias fotográficas registradas</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Estado y Fechas */}
            <Card>
              <CardHeader>
                <CardTitle>Estado Actual</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="text-center">
                    {getStatusBadge(reception.status)}
                  </div>
                  
                  <div className="space-y-3 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-500">Recibido:</span>
                      <span>{new Date(reception.entryDate).toLocaleDateString('es-MX')}</span>
                    </div>
                    

                    

                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Información del Recepcionista */}
            <Card>
              <CardHeader>
                <CardTitle>Recepcionista</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-3">
                  <User className="w-8 h-8 text-gray-400" />
                  <div>
                    <div className="font-medium">
                      {reception.receptionist?.name || 'No asignado'}
                    </div>
                    <div className="text-sm text-gray-500">
                      {formatFecha(reception.createdAt)}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Acciones Rápidas */}
            <Card>
              <CardHeader>
                <CardTitle>Acciones</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Button asChild className="w-full" variant="outline">
                    <Link href={`/reception/${reception.id}/editar`}>
                      <Edit className="w-4 h-4 mr-2" />
                      Editar Recepción
                    </Link>
                  </Button>
                  <Button className="w-full" variant="outline">
                    <Download className="w-4 h-4 mr-2" />
                    Generar Reporte
                  </Button>
                  <Button asChild className="w-full" variant="outline">
                    <Link href={`/vehiculos/${reception.vehicle.id}`}>
                      <Car className="w-4 h-4 mr-2" />
                      Ver Vehículo
                    </Link>
                  </Button>
                  <Button asChild className="w-full" variant="outline">
                    <Link href={`/clientes/${reception.client.id}`}>
                      <User className="w-4 h-4 mr-2" />
                      Ver Cliente
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
