"use client"

import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle, User, Building } from "lucide-react"
import { ClientType, type CreateClientData } from "@/types"

// Validation schema
const clientSchema = z.object({
  name: z.string().min(1, "El nombre es requerido").min(2, "El nombre debe tener al menos 2 caracteres"),
  phone: z.string().min(1, "El teléfono es requerido").regex(/^\d{10}$/, "El teléfono debe tener 10 dígitos"),
  email: z.string().email("Email inválido").optional().or(z.literal("")),
  clientType: z.nativeEnum(ClientType),
  businessName: z.string().optional(),
  taxId: z.string().optional(),
  address: z.string().optional(),
  paymentTerms: z.string().optional(),
  billingPeriod: z.string().optional(),
}).refine((data) => {
  // For fleet clients, business name is required
  if (data.clientType === ClientType.FLEET && !data.businessName?.trim()) {
    return false
  }
  return true
}, {
  message: "La razón social es requerida para clientes flotilleros",
  path: ["businessName"]
})

type ClientFormData = z.infer<typeof clientSchema>

interface ClientFormProps {
  onSubmit: (data: CreateClientData) => void
  onCancel?: () => void
  initialData?: Partial<CreateClientData>
  isLoading?: boolean
}

export function ClientForm({ onSubmit, onCancel, initialData, isLoading = false }: ClientFormProps) {
  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors }
  } = useForm<ClientFormData>({
    resolver: zodResolver(clientSchema),
    defaultValues: {
      name: "",
      phone: "",
      email: "",
      clientType: ClientType.INDIVIDUAL,
      businessName: "",
      taxId: "",
      address: "",
      paymentTerms: "",
      billingPeriod: "",
      ...initialData
    }
  })

  const clientType = watch("clientType")

  const handleFormSubmit = (data: ClientFormData) => {
    // Clean up empty strings
    const cleanData: CreateClientData = {
      name: data.name.trim(),
      phone: data.phone.trim(),
      clientType: data.clientType,
      ...(data.email && { email: data.email.trim() }),
      ...(data.businessName && { businessName: data.businessName.trim() }),
      ...(data.taxId && { taxId: data.taxId.trim() }),
      ...(data.address && { address: data.address.trim() }),
      ...(data.paymentTerms && { paymentTerms: data.paymentTerms }),
      ...(data.billingPeriod && { billingPeriod: data.billingPeriod })
    }
    
    onSubmit(cleanData)
  }

  return (
    <Card className="max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <User className="h-5 w-5" />
          Registro de Cliente
        </CardTitle>
        <CardDescription>
          Completa la información del cliente
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
          {/* Client Type Selection */}
          <div className="space-y-4">
            <Label>Tipo de Cliente</Label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div
                className={`p-4 border-2 rounded-lg cursor-pointer transition-colors ${
                  clientType === ClientType.INDIVIDUAL
                    ? 'border-primary bg-primary/5'
                    : 'border-border hover:border-primary/50'
                }`}
                onClick={() => setValue('clientType', ClientType.INDIVIDUAL)}
              >
                <div className="flex items-center gap-3">
                  <User className="w-6 h-6" />
                  <div>
                    <h3 className="font-medium">Cliente Individual</h3>
                    <p className="text-sm text-muted-foreground">
                      Cliente con uno o pocos vehículos
                    </p>
                  </div>
                </div>
              </div>

              <div
                className={`p-4 border-2 rounded-lg cursor-pointer transition-colors ${
                  clientType === ClientType.FLEET
                    ? 'border-primary bg-primary/5'
                    : 'border-border hover:border-primary/50'
                }`}
                onClick={() => setValue('clientType', ClientType.FLEET)}
              >
                <div className="flex items-center gap-3">
                  <Building className="w-6 h-6" />
                  <div>
                    <h3 className="font-medium">Cliente Flotillero</h3>
                    <p className="text-sm text-muted-foreground">
                      Empresa con múltiples vehículos
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Información Básica</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">
                  Nombre {clientType === ClientType.FLEET ? 'de Contacto' : 'Completo'} *
                </Label>
                <Input
                  id="name"
                  {...register("name")}
                  className={errors.name ? "border-red-500" : ""}
                  placeholder="Nombre del cliente"
                />
                {errors.name && (
                  <div className="flex items-center gap-2 text-sm text-red-500">
                    <AlertCircle className="h-4 w-4" />
                    {errors.name.message}
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="phone">Teléfono *</Label>
                <Input
                  id="phone"
                  {...register("phone")}
                  className={errors.phone ? "border-red-500" : ""}
                  placeholder="10 dígitos"
                />
                {errors.phone && (
                  <div className="flex items-center gap-2 text-sm text-red-500">
                    <AlertCircle className="h-4 w-4" />
                    {errors.phone.message}
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  {...register("email")}
                  className={errors.email ? "border-red-500" : ""}
                  placeholder="<EMAIL>"
                />
                {errors.email && (
                  <div className="flex items-center gap-2 text-sm text-red-500">
                    <AlertCircle className="h-4 w-4" />
                    {errors.email.message}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Tax Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Datos Fiscales</h3>
            <p className="text-sm text-muted-foreground">
              {clientType === ClientType.FLEET
                ? 'Información fiscal de la empresa (requerida para flotilleros)'
                : 'Información fiscal (opcional para clientes individuales)'
              }
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="businessName">
                  Razón Social {clientType === ClientType.FLEET ? '*' : ''}
                </Label>
                <Input
                  id="businessName"
                  {...register("businessName")}
                  className={errors.businessName ? "border-red-500" : ""}
                  placeholder="Nombre fiscal de la empresa"
                />
                {errors.businessName && (
                  <div className="flex items-center gap-2 text-sm text-red-500">
                    <AlertCircle className="h-4 w-4" />
                    {errors.businessName.message}
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="taxId">RFC</Label>
                <Input
                  id="taxId"
                  {...register("taxId")}
                  className={errors.taxId ? "border-red-500" : ""}
                  placeholder="RFC con homoclave"
                  onChange={(e) => {
                    e.target.value = e.target.value.toUpperCase()
                    setValue("taxId", e.target.value)
                  }}
                />
                {errors.taxId && (
                  <div className="flex items-center gap-2 text-sm text-red-500">
                    <AlertCircle className="h-4 w-4" />
                    {errors.taxId.message}
                  </div>
                )}
              </div>

              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="address">Dirección Fiscal</Label>
                <Textarea
                  id="address"
                  {...register("address")}
                  className={errors.address ? "border-red-500" : ""}
                  placeholder="Dirección completa"
                  rows={2}
                />
                {errors.address && (
                  <div className="flex items-center gap-2 text-sm text-red-500">
                    <AlertCircle className="h-4 w-4" />
                    {errors.address.message}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Fleet Configuration */}
          {clientType === ClientType.FLEET && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Configuración de Flotilla</h3>
              <p className="text-sm text-muted-foreground">
                Condiciones especiales para clientes flotilleros
              </p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="paymentTerms">Condiciones de Pago</Label>
                  <Select
                    value={watch("paymentTerms")}
                    onValueChange={(value) => setValue("paymentTerms", value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Seleccionar..." />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="CONTADO">Contado</SelectItem>
                      <SelectItem value="CREDITO_15">Crédito 15 días</SelectItem>
                      <SelectItem value="CREDITO_30">Crédito 30 días</SelectItem>
                      <SelectItem value="CREDITO_45">Crédito 45 días</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="billingPeriod">Período de Facturación</Label>
                  <Select
                    value={watch("billingPeriod")}
                    onValueChange={(value) => setValue("billingPeriod", value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Seleccionar..." />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="SEMANAL">Semanal</SelectItem>
                      <SelectItem value="QUINCENAL">Quincenal</SelectItem>
                      <SelectItem value="MENSUAL">Mensual</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          )}

          {/* Submit Buttons */}
          <div className="flex gap-3 pt-4">
            <Button type="submit" disabled={isLoading} className="flex-1">
              {isLoading ? 'Guardando...' : 'Guardar Cliente'}
            </Button>
            {onCancel && (
              <Button type="button" variant="outline" onClick={onCancel}>
                Cancelar
              </Button>
            )}
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
