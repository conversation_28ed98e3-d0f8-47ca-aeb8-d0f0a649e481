"use client"

import { useState, useContext, createContext, type ReactNode, useEffect } from "react"
import { UserRole } from "@/types"
import { Session, User as PrismaUser } from "@prisma/client"

interface RoleContextType {
  currentRole: UserRole
  user: PrismaUser | null
  setCurrentRole: (role: UserRole) => void
  currentWorkshopId: string | null
  setCurrentWorkshopId: (workshopId: string | null) => void
}

const RoleContext = createContext<RoleContextType | undefined>(undefined)

interface RoleProviderProps {
  children: ReactNode
  initialSession: { user: PrismaUser, session: Session }
}

export function RoleProvider({ children, initialSession }: RoleProviderProps) {
  // const [session] = useState<{ user: PrismaUser, session: Session }>(initialSession);
  const [user] = useState<PrismaUser | null>(initialSession.user)
  const [currentWorkshopId, setCurrentWorkshopId] = useState<string | null>(initialSession.user.currentWorkshopId || null)


  // Get user role from session, fallback to RECEPCIONISTA
  const currentRole = (user?.role as UserRole) || UserRole.RECEPCIONISTA

  const setCurrentRole = (role: UserRole) => {
    // Role changes must be managed by administrators
    console.warn("Role changes must be managed by administrators")
  }


  return (
    <RoleContext.Provider
      value={{
        currentRole,
        user,
        setCurrentRole,
        currentWorkshopId,
        setCurrentWorkshopId
      }}
    >
      {children}
    </RoleContext.Provider>
  )
}

export function useRole() {
  const context = useContext(RoleContext)
  if (context === undefined) {
    throw new Error("useRole must be used within a RoleProvider")
  }
  return context
}

// Helper hook to check if user has specific role
export function useHasRole(requiredRole: UserRole | UserRole[]) {
  const { currentRole } = useRole()
  
  if (Array.isArray(requiredRole)) {
    return requiredRole.includes(currentRole)
  }
  
  return currentRole === requiredRole
}

// Helper hook to check if user is admin
export function useIsAdmin() {
  return useHasRole(UserRole.ADMIN)
}
