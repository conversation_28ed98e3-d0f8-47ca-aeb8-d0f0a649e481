import { Elysia, t } from 'elysia'
import { ReceptionService } from './reception.service'
import { CreateReceptionSchema, UpdateReceptionSchema } from './reception.dto'
import { requireRole } from '../auth/auth.middleware'
import { PrismaClient } from '@prisma/client'
import { HttpException } from '@/lib/exceptions/HttpExceptions'

const prisma = new PrismaClient()

export const receptionController = new Elysia({ prefix: '/receptions' })
  .use(requireRole(['RECEPCIONISTA', 'JEFE_TALLER', 'ADMIN']))
  
  .get('/', async ({ query, user, status }) => {
    const workshopId = user.currentWorkshopId;
    if (user.role !== 'ADMIN' && !workshopId) {
      throw HttpException.Forbidden('No workshop selected. Please select a workshop first')
    }

    const receptions = await ReceptionService.list({
      status: query.status,
      clientId: query.clientId,
      vehicleId: query.vehicleId,
      assignedTech: query.assignedTech,
      dateFrom: query.dateFrom ? new Date(query.dateFrom) : undefined,
      dateTo: query.dateTo ? new Date(query.dateTo) : undefined,
      workshopId
    });

    return {
      success: true,
      data: receptions
    };
  }, {
    query: t.Object({
      status: t.Optional(t.String()),
      clientId: t.Optional(t.String()),
      vehicleId: t.Optional(t.String()),
      assignedTech: t.Optional(t.String()),
      dateFrom: t.Optional(t.String()),
      dateTo: t.Optional(t.String())
    })
  })
  
  .post('/', async ({ body, user }) => {
    console.log('Usuario en controlador:', user) // Para debug

    // Get user's current workshop
    const userWithWorkshop = await prisma.user.findUnique({
      where: { id: user.id },
      select: { currentWorkshopId: true }
    })

    if (!userWithWorkshop?.currentWorkshopId) {
      return {
        success: false,
        error: "No workshop selected. Please select a workshop first."
      }
    }

    const reception = await ReceptionService.create(body, user.id, userWithWorkshop.currentWorkshopId);

    return {
      success: true,
      data: reception,
      message: "Recepción registrada exitosamente"
    };
  }, {
    body: CreateReceptionSchema
  })
  
  .get('/activas', async () => {
      const receptions = await ReceptionService.getActive();
      
      return {
        success: true,
        data: receptions
      };
  
  })
  
  .get('/stats', async () => {
      const stats = await ReceptionService.getStats();
      
      return {
        success: true,
        data: stats
      };
  })
  
  .get('/:id', async ({ params }) => {
      const recepcion = await ReceptionService.getById(params.id);
      
      return {
        success: true,
        data: recepcion
      };
  }, {
    params: t.Object({
      id: t.String()
    })
  })
  
  .put('/:id', async ({ params, body }) => {
      const recepcion = await ReceptionService.update(params.id, body);
      
      return {
        success: true,
        data: recepcion,
        message: "Recepción actualizada exitosamente"
      };
   
  }, {
    params: t.Object({
      id: t.String()
    }),
    body: UpdateReceptionSchema
  })
  
  .delete('/:id', async ({ params }) => {
      await ReceptionService.delete(params.id);
      
      return {
        success: true,
        message: "Recepción eliminada exitosamente"
      };
  
  }, {
    params: t.Object({
      id: t.String()
    })
  })
