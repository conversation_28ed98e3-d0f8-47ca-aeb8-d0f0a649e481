'use client'

import { authClient } from '@/lib/auth-client'
import { useQuery } from '@tanstack/react-query'

export function useSession() {
  return useQuery({
    queryKey: ['session'],
    queryFn: async () => {
      const session = await authClient.getSession()
      return session
    },
    staleTime: 1000 * 60 * 5, // 5 minutos
    refetchOnWindowFocus: false,
  })
}

export function useUser() {
  const { data: session, ...rest } = useSession()
  return {
    user: session?.user || null,
    ...rest
  }
}
