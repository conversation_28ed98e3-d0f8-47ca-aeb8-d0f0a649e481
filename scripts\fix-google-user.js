const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function fixGoogleUser() {
  console.log('🔧 Arreglando usuario de Google...')

  try {
    // Find the Google user
    const googleUser = await prisma.user.findFirst({
      where: { 
        email: '<EMAIL>'
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    if (!googleUser) {
      console.log('❌ Usuario de Google no encontrado')
      return
    }

    console.log('✅ Usuario encontrado:', googleUser.name, googleUser.email)

    // Find the default workshop
    const defaultWorkshop = await prisma.workshop.findUnique({
      where: { id: 'default-workshop-ruedda' }
    })

    if (!defaultWorkshop) {
      console.log('❌ Taller por defecto no encontrado')
      return
    }

    console.log('✅ Taller encontrado:', defaultWorkshop.name)

    // Update user to have admin role and assign to workshop
    await prisma.user.update({
      where: { id: googleUser.id },
      data: {
        role: 'ADMIN', // Make them admin
        currentWorkshopId: defaultWorkshop.id
      }
    })

    console.log('✅ Usuario actualizado con rol ADMIN y taller asignado')

    // Create UserWorkshop relationship
    await prisma.userWorkshop.upsert({
      where: {
        userId_workshopId: {
          userId: googleUser.id,
          workshopId: defaultWorkshop.id
        }
      },
      update: {
        role: 'ADMIN',
        isActive: true
      },
      create: {
        userId: googleUser.id,
        workshopId: defaultWorkshop.id,
        role: 'ADMIN',
        isActive: true,
        assignedAt: new Date()
      }
    })

    console.log('✅ Relación UserWorkshop creada')

    console.log('🎉 Usuario arreglado exitosamente:')
    console.log(`   • Usuario: ${googleUser.name} (${googleUser.email})`)
    console.log(`   • Rol: ADMIN`)
    console.log(`   • Taller: ${defaultWorkshop.name}`)
    console.log(`   • Estado: Activo`)

  } catch (error) {
    console.error('❌ Error arreglando usuario:', error)
  } finally {
    await prisma.$disconnect()
  }
}

fixGoogleUser()
