import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'
import { getServerSession } from '@/lib/getSession'

const prisma = new PrismaClient()

export async function POST(request: NextRequest) {
  try {
    // Verify user is authenticated
    const session = await getServerSession({ shouldRedirect: false })
    
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { workshopId } = body

    if (!workshopId) {
      return NextResponse.json(
        { error: 'Workshop ID is required' },
        { status: 400 }
      )
    }

    // Verify user has access to this workshop
    const userWorkshop = await prisma.userWorkshop.findUnique({
      where: {
        userId_workshopId: {
          userId: session.user.id,
          workshopId: workshopId
        }
      },
      include: {
        workshop: true
      }
    })

    if (!userWorkshop || !userWorkshop.isActive) {
      return NextResponse.json(
        { error: 'You do not have access to this workshop' },
        { status: 403 }
      )
    }

    // Update user's current workshop
    await prisma.user.update({
      where: { id: session.user.id },
      data: { currentWorkshopId: workshopId }
    })

    return NextResponse.json({
      success: true,
      workshop: {
        id: userWorkshop.workshop.id,
        name: userWorkshop.workshop.name,
        location: userWorkshop.workshop.location
      }
    })

  } catch (error) {
    console.error('Error switching workshop:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
