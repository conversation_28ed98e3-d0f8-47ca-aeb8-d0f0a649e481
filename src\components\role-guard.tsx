"use client"

import { type ReactNode } from "react"
import { UserRole } from "@/types"
import { useRole } from "@/hooks/use-role"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertTriangle } from "lucide-react"

interface RoleGuardProps {
  children: ReactNode
  allowedRoles: UserRole[]
  fallback?: ReactNode
  showError?: boolean
}

export function RoleGuard({ 
  children, 
  allowedRoles, 
  fallback,
  showError = true 
}: RoleGuardProps) {
  const { currentRole, isLoading } = useRole()

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  // Check if user has required role
  const hasAccess = allowedRoles.includes(currentRole)

  if (!hasAccess) {
    if (fallback) {
      return <>{fallback}</>
    }

    if (showError) {
      return (
        <div className="p-6">
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              No tienes permisos para acceder a esta sección. 
              Se requiere uno de los siguientes roles: {allowedRoles.join(", ")}
            </AlertDescription>
          </Alert>
        </div>
      )
    }

    return null
  }

  return <>{children}</>
}

// Specific role guards for common use cases
export function AdminGuard({ children, fallback }: { children: ReactNode, fallback?: ReactNode }) {
  return (
    <RoleGuard allowedRoles={[UserRole.ADMIN]} fallback={fallback}>
      {children}
    </RoleGuard>
  )
}

export function ReceptionGuard({ children, fallback }: { children: ReactNode, fallback?: ReactNode }) {
  return (
    <RoleGuard 
      allowedRoles={[UserRole.ADMIN, UserRole.RECEPCIONISTA, UserRole.JEFE_TALLER]} 
      fallback={fallback}
    >
      {children}
    </RoleGuard>
  )
}

export function TechnicianGuard({ children, fallback }: { children: ReactNode, fallback?: ReactNode }) {
  return (
    <RoleGuard 
      allowedRoles={[UserRole.ADMIN, UserRole.TECNICO, UserRole.JEFE_TALLER]} 
      fallback={fallback}
    >
      {children}
    </RoleGuard>
  )
}

export function ManagerGuard({ children, fallback }: { children: ReactNode, fallback?: ReactNode }) {
  return (
    <RoleGuard 
      allowedRoles={[UserRole.ADMIN, UserRole.JEFE_TALLER]} 
      fallback={fallback}
    >
      {children}
    </RoleGuard>
  )
}
