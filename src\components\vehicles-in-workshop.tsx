"use client"

import { <PERSON>, <PERSON><PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { StatusBadge } from "@/components/status-badge"
import { <PERSON>, Clock, User, Eye } from "lucide-react"
import Link from "next/link"
import { useQuery } from "@tanstack/react-query"
import { server } from "@/app/api/server"

interface VehicleInWorkshop {
  id: string
  brand: string
  model: string
  year: number
  licensePlate: string
  client: {
    name: string
    clientType: 'INDIVIDUAL' | 'FLEET'
  }
  reception: {
    id: string
    status: string
    entryDate: string
    estimatedCompletion?: string
  }
}

export function VehiclesInWorkshop() {
  const { data: vehiclesData, isLoading } = useQuery({
    queryKey: ['vehicles-in-workshop'],
    queryFn: async () => {
      // Mock data for now - replace with actual API call
      const mockData: VehicleInWorkshop[] = [
        {
          id: "1",
          brand: "Toyota",
          model: "<PERSON>roll<PERSON>",
          year: 2020,
          licensePlate: "ABC-123",
          client: {
            name: "<PERSON>",
            clientType: "INDIVIDUAL"
          },
          reception: {
            id: "r1",
            status: "IN_REPAIR",
            entryDate: "2024-01-15T10:00:00Z",
            estimatedCompletion: "2024-01-20T16:00:00Z"
          }
        },
        {
          id: "2", 
          brand: "Ford",
          model: "F-150",
          year: 2019,
          licensePlate: "XYZ-789",
          client: {
            name: "Transportes del Norte S.A.",
            clientType: "FLEET"
          },
          reception: {
            id: "r2",
            status: "WAITING_PARTS",
            entryDate: "2024-01-14T08:30:00Z"
          }
        },
        {
          id: "3",
          brand: "Nissan",
          model: "Sentra",
          year: 2021,
          licensePlate: "DEF-456",
          client: {
            name: "María González",
            clientType: "INDIVIDUAL"
          },
          reception: {
            id: "r3",
            status: "COMPLETED",
            entryDate: "2024-01-12T14:15:00Z"
          }
        }
      ]
      
      return { success: true, data: mockData }
    }
  })

  const vehicles = vehiclesData?.success ? vehiclesData.data : []

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('es-MX', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    })
  }

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('es-MX', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Vehículos en Taller</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="animate-pulse">
                <div className="h-20 bg-muted rounded-lg"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Car className="h-5 w-5" />
          Vehículos en Taller
        </CardTitle>
        <CardDescription>
          Estado actual de los vehículos en el taller
        </CardDescription>
      </CardHeader>
      <CardContent>
        {vehicles.length === 0 ? (
          <div className="text-center py-8">
            <Car className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <p className="text-muted-foreground">No hay vehículos en el taller</p>
          </div>
        ) : (
          <div className="space-y-4">
            {vehicles.map((vehicle) => (
              <div
                key={vehicle.id}
                className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors"
              >
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <div>
                      <h4 className="font-semibold">
                        {vehicle.brand} {vehicle.model} {vehicle.year}
                      </h4>
                      <p className="text-sm text-muted-foreground">
                        {vehicle.licensePlate}
                      </p>
                    </div>
                    <StatusBadge status={vehicle.reception.status} />
                  </div>
                  
                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <User className="h-3 w-3" />
                      <span>{vehicle.client.name}</span>
                      <StatusBadge 
                        status={vehicle.client.clientType} 
                        size="sm"
                      />
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      <span>Entrada: {formatDate(vehicle.reception.entryDate)}</span>
                    </div>
                    {vehicle.reception.estimatedCompletion && (
                      <div className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        <span>Est. entrega: {formatDate(vehicle.reception.estimatedCompletion)}</span>
                      </div>
                    )}
                  </div>
                </div>
                
                <div className="flex gap-2">
                  <Button asChild size="sm" variant="outline">
                    <Link href={`/recepcion/${vehicle.reception.id}`}>
                      <Eye className="h-4 w-4 mr-1" />
                      Ver detalles
                    </Link>
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )}
        
        {vehicles.length > 0 && (
          <div className="mt-4 pt-4 border-t">
            <Button asChild variant="outline" className="w-full">
              <Link href="/vehiculos">
                Ver todos los vehículos
              </Link>
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
