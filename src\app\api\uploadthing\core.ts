import { createUploadthing, type FileRouter } from "uploadthing/next";
import { auth } from "@/lib/auth";

const f = createUploadthing();

export const ourFileRouter = {
  // Evidencias fotográficas para recepciones
  evidenciaFoto: f({ image: { maxFileSize: "10MB", maxFileCount: 5 } })
    .middleware(async ({ req }) => {
      // Verificar autenticación
      const session = await auth.api.getSession({ headers: req.headers });
      
      if (!session) throw new Error("No autorizado");
      
      // Solo recepcionistas, jefes de taller y admins pueden subir evidencias
      if (!['RECEPCIONISTA', 'JEFE_TALLER', 'ADMIN'].includes(session.user.role || '')) {
        throw new Error("No tienes permisos para subir evidencias");
      }

      return { userId: session.user.id };
    })
    .onUploadComplete(async ({ metadata, file }) => {
      console.log("Evidencia subida por usuario:", metadata.userId);
      console.log("URL del archivo:", file.url);
      
      return { uploadedBy: metadata.userId, url: file.url };
    }),
} satisfies FileRouter;

export type OurFileRouter = typeof ourFileRouter;
