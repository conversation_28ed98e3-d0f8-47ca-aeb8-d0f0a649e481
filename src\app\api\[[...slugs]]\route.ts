// app/api/[[...slugs]]/route.ts
import { Elysia } from 'elysia'
import { userController } from './users/user.controller'
import { invitationController } from './invitations/invitation.controller'
import { publicController } from './public/public.controller'
import { clientController } from './clients/client.controller'
import { vehicleController } from './vehicles/vehicle.controller'
import { receptionController } from './receptions/reception.controller'
import { workshopController } from './workshops/workshop.controller'
import { onError } from './onError'

const app = new Elysia({ prefix: '/api' })
    .state({
        start: 0,
        requestId: ''
    })
    .onError(onError)
    .use(userController)
    .use(invitationController)
    .use(publicController)
    .use(clientController)
    .use(vehicleController)
    .use(receptionController)
    .use(workshopController)

export type App = typeof app;

export const GET = app.handle
export const POST = app.handle
export const PUT = app.handle
export const DELETE = app.handle