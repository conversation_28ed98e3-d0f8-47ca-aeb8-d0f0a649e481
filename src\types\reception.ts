// Reception types adapted from current Prisma schema

// Enums from current schema
export enum ServiceType {
  MINOR_PREVENTIVE = "MINOR_PREVENTIVE",
  MAJOR_PREVENTIVE = "MAJOR_PREVENTIVE", 
  SPARK_PLUG_CHANGE = "SPARK_PLUG_CHANGE",
  DIAGNOSTIC = "DIAGNOSTIC",
  BODYWORK_PAINT = "BODYWORK_PAINT",
  OTHER = "OTHER",
}

export enum TiresCondition {
  GOOD_APPEARANCE = "GOOD_APPEARANCE",
  SOME_DETAILS = "SOME_DETAILS",
}

export enum ReceptionStatus {
  RECEIVED = "RECEIVED",
  IN_DIAGNOSTIC = "IN_DIAGNOSTIC", 
  WAITING_APPROVAL = "WAITING_APPROVAL",
  WAITING_PARTS = "WAITING_PARTS",
  IN_REPAIR = "IN_REPAIR",
  COMPLETED = "COMPLETED",
  DELIVERED = "DELIVERED",
}

export enum EvidenceType {
  VIN = "VIN",
  DASHBOARD = "DASHBOARD",
  FRONT = "FRONT",
  LEFT_FRONT_SIDE = "LEFT_FRONT_SIDE",
  DRIVER_SEAT = "DRIVER_SEAT",
  LEFT_REAR_SIDE = "LEFT_REAR_SIDE",
  LEFT_REAR_SEAT = "LEFT_REAR_SEAT",
  REAR = "REAR",
  TRUNK = "TRUNK",
  SPARE_TIRE = "SPARE_TIRE",
  RIGHT_REAR_SIDE = "RIGHT_REAR_SIDE",
  RIGHT_REAR_SEAT = "RIGHT_REAR_SEAT",
  RIGHT_FRONT_SIDE = "RIGHT_FRONT_SIDE",
  PASSENGER_SEAT = "PASSENGER_SEAT",
  EXTERIOR_DAMAGE = "EXTERIOR_DAMAGE",
  ENGINE_BAY = "ENGINE_BAY",
}

// Photo evidence interface
export interface PhotoEvidence {
  id: string
  url: string
  type: EvidenceType
  description?: string
  receptionId: string
  createdAt: Date
}

// Reception interface matching current Prisma schema
export interface Reception {
  id: string
  entryDate: Date
  mileage: number
  serviceType: ServiceType
  otherService?: string
  observations?: string
  tiresCondition?: TiresCondition
  assignedTech?: string
  status: ReceptionStatus

  // Relations
  clientId: string
  vehicleId: string
  receptionistId: string
  
  // Metadata
  createdAt: Date
  updatedAt: Date

  // Relations (populated when needed)
  client?: Client
  vehicle?: Vehicle
  receptionist?: User
  evidence?: PhotoEvidence[]
}

// Reception creation data
export interface CreateReceptionData {
  mileage: number
  serviceType: ServiceType
  otherService?: string
  observations?: string
  tiresCondition?: TiresCondition
  assignedTech?: string
  clientId: string
  vehicleId: string
  receptionistId: string
}

// Reception update data
export interface UpdateReceptionData {
  mileage?: number
  serviceType?: ServiceType
  otherService?: string
  observations?: string
  tiresCondition?: TiresCondition
  assignedTech?: string
  status?: ReceptionStatus
}

// Reception filters
export interface ReceptionFilters {
  status?: ReceptionStatus
  serviceType?: ServiceType
  clientId?: string
  vehicleId?: string
  receptionistId?: string
  dateFrom?: Date
  dateTo?: Date
}

// Import types that will be used in relations
import type { Client } from './client'
import type { Vehicle } from './vehicle'
import type { User } from './user'
