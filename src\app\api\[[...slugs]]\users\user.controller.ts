import { UserService } from "./user.service";
import { Elysia, t } from "elysia";

export const userController = new Elysia({ prefix: '/users' })
    .get('/', async () => {
        return await UserService.listUsers();
    })
    .post('/', async ({ body }) => {
        // return await UserService.createUser(body);
    })
    .get('/clone/:id', async ({ params, query }) => {
        return await UserService.cloneUserById(params.id, query);
    }, {
        params: t.Object({
            id: t.String()
        }),
        query: t.Object({
            name: t.String(),
            other: t.Optional(t.String())
        })
    })
