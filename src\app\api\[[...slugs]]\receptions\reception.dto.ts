import { t } from 'elysia'

export const CreateReceptionSchema = t.Object({
  clientId: t.String(),
  vehicleId: t.String(),
  mileage: t.Number({ minimum: 0 }),
  serviceType: t.Union([
    t.Literal('MINOR_PREVENTIVE'),
    t.Literal('MAJOR_PREVENTIVE'),
    t.<PERSON>teral('SPARK_PLUG_CHANGE'),
    t.<PERSON>('DIAGNOSTIC'),
    t.<PERSON>teral('BODYWORK_PAINT'),
    t.Literal('OTHER')
  ]),
  otherService: t.Optional(t.String()),
  observations: t.Optional(t.String()),
  tiresCondition: t.Optional(t.Union([
    t.Literal('GOOD_APPEARANCE'),
    t.<PERSON>teral('SOME_DETAILS')
  ])),
  assignedTech: t.Optional(t.String()),
  evidence: t.Array(t.Object({
    type: t.Union([
      t.Literal('VIN'),
      t.Literal('DASHBOARD'),
      t.Literal('FRONT'),
      t.Literal('LEFT_FRONT_SIDE'),
      t.Literal('DRIVER_SEAT'),
      t.Literal('LEFT_REAR_SIDE'),
      t.Literal('LEFT_REAR_SEAT'),
      t.Literal('REAR'),
      t.Literal('TRUNK'),
      t.Literal('SPARE_TIRE'),
      t.Literal('RIGHT_REAR_SIDE'),
      t.Literal('RIGHT_REAR_SEAT'),
      t.Literal('RIGHT_FRONT_SIDE'),
      t.Literal('PASSENGER_SEAT'),
      t.Literal('EXTERIOR_DAMAGE'),
      t.Literal('ENGINE_BAY')
    ]),
    url: t.String(),
    description: t.Optional(t.String())
  }))
})

export const UpdateReceptionSchema = t.Object({
  mileage: t.Optional(t.Number({ minimum: 0 })),
  serviceType: t.Optional(t.Union([
    t.Literal('MINOR_PREVENTIVE'),
    t.Literal('MAJOR_PREVENTIVE'),
    t.Literal('SPARK_PLUG_CHANGE'),
    t.Literal('DIAGNOSTIC'),
    t.Literal('BODYWORK_PAINT'),
    t.Literal('OTHER')
  ])),
  otherService: t.Optional(t.String()),
  observations: t.Optional(t.String()),
  tiresCondition: t.Optional(t.Union([
    t.Literal('GOOD_APPEARANCE'),
    t.Literal('SOME_DETAILS')
  ])),
  assignedTech: t.Optional(t.String()),
  status: t.Optional(t.Union([
    t.Literal('RECEIVED'),
    t.Literal('IN_DIAGNOSTIC'),
    t.Literal('WAITING_APPROVAL'),
    t.Literal('WAITING_PARTS'),
    t.Literal('IN_REPAIR'),
    t.Literal('COMPLETED'),
    t.Literal('DELIVERED')
  ]))
})

export const ReceptionResponseSchema = t.Object({
  id: t.String(),
  entryDate: t.Date(),
  mileage: t.Number(),
  serviceType: t.String(),
  otherService: t.Optional(t.String()),
  observations: t.Optional(t.String()),
  tiresCondition: t.Optional(t.String()),
  assignedTech: t.Optional(t.String()),
  status: t.String(),
  createdAt: t.Date(),
  updatedAt: t.Date()
})

export type CreateReceptionInput = typeof CreateReceptionSchema.static
export type UpdateReceptionInput = typeof UpdateReceptionSchema.static
export type ReceptionResponse = typeof ReceptionResponseSchema.static
