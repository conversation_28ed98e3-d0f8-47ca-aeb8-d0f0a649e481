import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'
import { cookies } from 'next/headers'
import { SignJWT } from 'jose'

const prisma = new PrismaClient()

export async function POST(request: NextRequest) {
  try {
    const { userId, email, name, role } = await request.json()

    // Verify this is a development environment
    if (process.env.NODE_ENV === 'production') {
      return NextResponse.json(
        { error: 'Dev login not available in production' },
        { status: 403 }
      )
    }

    // Find the user in the database
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        workshopAccess: {
          include: {
            workshop: true
          }
        },
        currentWorkshop: true
      }
    })

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Create a simple session token
    const secret = new TextEncoder().encode(
      process.env.NEXTAUTH_SECRET || 'dev-secret-key'
    )

    const token = await new SignJWT({
      sub: user.id,
      email: user.email,
      name: user.name,
      role: user.role,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60) // 24 hours
    })
      .setProtectedHeader({ alg: 'HS256' })
      .sign(secret)

    // Set the session cookie
    const cookieStore = cookies()
    cookieStore.set('dev-session', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 24 * 60 * 60 // 24 hours
    })

    return NextResponse.json({
      success: true,
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role,
        currentWorkshop: user.currentWorkshop
      }
    })

  } catch (error) {
    console.error('Dev login error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
