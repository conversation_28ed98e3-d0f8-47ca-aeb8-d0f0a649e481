'use client'

import { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { RefreshCw, Mail } from 'lucide-react'
import { DataTable } from '@/components/ui/data-table'
import { useInvitations } from '@/hooks/use-invitations'
import { invitationsColumns } from './invitations-columns'
import { InvitationsPagination } from './invitations-pagination'
import { useQueryClient } from '@tanstack/react-query'
import { USER_INVITATIONS } from '@/constants/queries-key'

interface InvitationsTableProps {
  searchParams: {
    page?: string
    status?: string
    role?: string
    search?: string
  }
}

export function InvitationsTable({ searchParams }: InvitationsTableProps) {
  const queryClient = useQueryClient()

  const page = parseInt(searchParams.page || '1')
  const status = searchParams.status
  const role = searchParams.role
  const search = searchParams.search

  const { data, isLoading, error } = useInvitations({
    page,
    status,
    role,
    search
  })

  const handleRefresh = () => {
    queryClient.invalidateQueries({ queryKey: [USER_INVITATIONS] })
  }

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <RefreshCw className="h-6 w-6 animate-spin mr-2" />
          Cargando invitaciones...
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center">
            <p className="text-red-600 mb-4">Error al cargar las invitaciones</p>
            <Button onClick={handleRefresh}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Reintentar
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  const invitations = data?.invitations || []
  const pagination = data?.pagination

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Invitaciones ({pagination?.totalCount || 0})</span>
            <Button variant="outline" size="sm" onClick={handleRefresh}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Actualizar
            </Button>
          </CardTitle>
          <CardDescription>
            Gestiona las invitaciones enviadas a nuevos usuarios
          </CardDescription>
        </CardHeader>
        <CardContent>
          {invitations.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Mail className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No hay invitaciones que mostrar</p>
            </div>
          ) : (
            <DataTable
              columns={invitationsColumns}
              data={invitations}
            />
          )}
        </CardContent>
      </Card>

      {pagination && invitations.length > 0 && (
        <InvitationsPagination
          currentPage={pagination.currentPage}
          totalPages={pagination.totalPages}
          totalCount={pagination.totalCount}
          itemsPerPage={pagination.limit}
        />
      )}
    </div>
  )
}
