'use client'

import { useSearchParams } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { ShieldX, Mail, ArrowLeft, UserX, Clock, AlertTriangle } from 'lucide-react'
import Link from 'next/link'

const ERROR_MESSAGES = {
  'unable_to_create_user': {
    title: 'Acceso No Autorizado',
    description: 'No tienes una invitación válida para acceder al sistema',
    icon: UserX,
    color: 'text-red-600',
    bgColor: 'bg-red-50',
    borderColor: 'border-red-200'
  },
  'invitation_expired': {
    title: 'Invitación Expirada',
    description: 'Tu invitación ha expirado',
    icon: Clock,
    color: 'text-orange-600',
    bgColor: 'bg-orange-50',
    borderColor: 'border-orange-200'
  },
  'default': {
    title: 'Error de Autenticación',
    description: 'Ocurrió un error durante el proceso de autenticación',
    icon: AlertTriangle,
    color: 'text-red-600',
    bgColor: 'bg-red-50',
    borderColor: 'border-red-200'
  }
}

export default function AuthErrorPage() {
  const searchParams = useSearchParams()
  const error = searchParams.get('error') || 'default'
  
  const errorConfig = ERROR_MESSAGES[error as keyof typeof ERROR_MESSAGES] || ERROR_MESSAGES.default
  const Icon = errorConfig.icon

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="max-w-md w-full">
        <Card className="shadow-lg">
          <CardHeader className="text-center pb-4">
            <div className={`mx-auto w-16 h-16 ${errorConfig.bgColor} ${errorConfig.borderColor} border-2 rounded-full flex items-center justify-center mb-4`}>
              <Icon className={`h-8 w-8 ${errorConfig.color}`} />
            </div>
            <CardTitle className="text-2xl font-bold text-gray-900">
              {errorConfig.title}
            </CardTitle>
            <CardDescription className="text-gray-600 mt-2">
              {errorConfig.description}
            </CardDescription>
          </CardHeader>
          
          <CardContent className="space-y-6">
            {error === 'unable_to_create_user' && (
              <Alert className={`${errorConfig.bgColor} ${errorConfig.borderColor}`}>
                <ShieldX className={`h-4 w-4 ${errorConfig.color}`} />
                <AlertDescription className={errorConfig.color}>
                  <strong>¿Por qué veo este mensaje?</strong>
                  <br />
                  Solo los usuarios que han sido invitados por un administrador pueden acceder al sistema.
                </AlertDescription>
              </Alert>
            )}

            <div className="space-y-4">
              <div className="text-center">
                <h3 className="font-semibold text-gray-900 mb-2">¿Qué puedes hacer?</h3>
                <div className="space-y-3 text-sm text-gray-600">
                  <div className="flex items-start gap-3">
                    <Mail className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
                    <div className="text-left">
                      <p className="font-medium text-gray-900">Contacta al administrador</p>
                      <p>Solicita una invitación para acceder al sistema</p>
                    </div>
                  </div>
                  
                  {error === 'invitation_expired' && (
                    <div className="flex items-start gap-3">
                      <Clock className="h-5 w-5 text-orange-600 mt-0.5 flex-shrink-0" />
                      <div className="text-left">
                        <p className="font-medium text-gray-900">Solicita una nueva invitación</p>
                        <p>Tu invitación anterior ha expirado, necesitas una nueva</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <div className="pt-4 border-t">
                <div className="flex flex-col gap-3">
                  <Button asChild className="w-full">
                    <Link href="/">
                      <ArrowLeft className="h-4 w-4 mr-2" />
                      Volver al Inicio
                    </Link>
                  </Button>
                  
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="text-center mt-6">
          <p className="text-xs text-gray-500">
            Sistema de Gestión de Talleres
          </p>
        </div>
      </div>
    </div>
  )
}
