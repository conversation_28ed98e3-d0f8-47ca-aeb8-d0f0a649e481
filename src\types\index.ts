// Central export file for all types

// User types
export type { User, CreateUserData, UpdateUserData, Workshop } from './user'
export { UserRole } from './user'

// Client types  
export type { Client, CreateClientData, UpdateClientData, ClientFilters } from './client'
export { ClientType } from './client'

// Vehicle types
export type { 
  Vehicle, 
  CreateVehicleData, 
  UpdateVehicleData, 
  VehicleFilters,
  VehicleWithStatus 
} from './vehicle'
export { FuelType, TransmissionType } from './vehicle'

// Reception types
export type { 
  Reception, 
  CreateReceptionData, 
  UpdateReceptionData, 
  ReceptionFilters,
  PhotoEvidence 
} from './reception'
export { 
  ServiceType, 
  TiresCondition, 
  ReceptionStatus, 
  EvidenceType 
} from './reception'

// Common utility types
export interface PaginationParams {
  page?: number
  limit?: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export interface PaginatedResponse<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

// Dashboard statistics types
export interface DashboardStats {
  vehiclesInWorkshop: number
  pendingServices: number
  monthlyRevenue: number
  activeClients: number
  recentActivity: RecentActivity[]
}

export interface RecentActivity {
  id: string
  type: 'reception' | 'completion' | 'client_created' | 'vehicle_added'
  description: string
  timestamp: Date
  relatedId?: string
}
