import { Elysia, t } from 'elysia'
import { InvitationService } from './invitation.service'
import { CreateInvitationSchema } from './invitation.types'
import { requireAdmin, authMiddleware } from '../auth/auth.middleware'

export const invitationController = new Elysia({ prefix: '/invitations' })
  .use(requireAdmin)
  .post('/', async ({ body, user }) => {
    try {
      const invitation = await InvitationService.createInvitation(body, user.id);
      
      return {
        success: true,
        data: invitation,
        message: "Invitación creada exitosamente"
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Error desconocido"
      };
    }
  }, {
    body: CreateInvitationSchema
  })
  
  .get('/', async ({ query }) => {
    try {
      const page = query.page ? parseInt(query.page) : 1
      const limit = query.limit ? parseInt(query.limit) : 10
      const status = query.status || undefined
      const role = query.role || undefined
      const search = query.search || undefined

      const result = await InvitationService.listInvitations({
        page,
        limit,
        status,
        role,
        search
      });

      return {
        success: true,
        data: result
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Error desconocido"
      };
    }
  }, {
    query: t.Object({
      page: t.Optional(t.String()),
      limit: t.Optional(t.String()),
      status: t.Optional(t.String()),
      role: t.Optional(t.String()),
      search: t.Optional(t.String())
    })
  })
  
  .get('/token/:token', async ({ params }) => {
    try {
      const invitation = await InvitationService.getInvitationByToken(params.token);

      return {
        success: true,
        data: invitation
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Error desconocido"
      };
    }
  }, {
    params: t.Object({
      token: t.String()
    })
  })
  
  .delete('/:id', async ({ params }) => {
    try {
      const invitation = await InvitationService.cancelInvitation(params.id);
      
      return {
        success: true,
        data: invitation,
        message: "Invitación cancelada exitosamente"
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Error desconocido"
      };
    }
  }, {
    params: t.Object({
      id: t.String()
    })
  })
  
  .post('/:id/cancel', async ({ params }) => {
    try {
      const invitation = await InvitationService.cancelInvitation(params.id);

      return {
        success: true,
        data: invitation,
        message: "Invitación cancelada exitosamente"
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Error desconocido"
      };
    }
  }, {
    params: t.Object({
      id: t.String()
    })
  })

  .post('/:id/resend', async ({ params }) => {
    try {
      const invitation = await InvitationService.resendInvitation(params.id);

      return {
        success: true,
        data: invitation,
        message: "Invitación reenviada exitosamente"
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Error desconocido"
      };
    }
  }, {
    params: t.Object({
      id: t.String()
    })
  })

  .post('/accept/:token', async ({ params, headers }) => {
    try {
      // TODO: Obtener userId del usuario autenticado
      const userId = "temp-user-id";

      const invitation = await InvitationService.acceptInvitation(params.token, userId);

      return {
        success: true,
        data: invitation,
        message: "Invitación aceptada exitosamente"
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Error desconocido"
      };
    }
  }, {
    params: t.Object({
      token: t.String()
    })
  })
