import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient, UserRole } from '@prisma/client'
import { getServerSession } from '@/lib/getSession'

const prisma = new PrismaClient()

export async function POST(request: NextRequest) {
  try {
    // Verify user is authenticated and is admin
    const session = await getServerSession({ shouldRedirect: false })
    
    if (!session || session.user.role !== UserRole.ADMIN) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin access required.' },
        { status: 403 }
      )
    }

    const body = await request.json()
    const { name, location, phone, email, isActive } = body

    // Validate required fields
    if (!name || !location) {
      return NextResponse.json(
        { error: 'Name and location are required' },
        { status: 400 }
      )
    }

    // Create the workshop
    const workshop = await prisma.workshop.create({
      data: {
        name,
        location,
        phone: phone || null,
        email: email || null,
        isActive: isActive ?? true,
        settings: {
          businessHours: {
            monday: { open: '08:00', close: '18:00' },
            tuesday: { open: '08:00', close: '18:00' },
            wednesday: { open: '08:00', close: '18:00' },
            thursday: { open: '08:00', close: '18:00' },
            friday: { open: '08:00', close: '18:00' },
            saturday: { open: '08:00', close: '14:00' },
            sunday: { closed: true }
          },
          currency: 'MXN',
          timezone: 'America/Mexico_City',
          features: {
            photoEvidence: true,
            diagnostics: true,
            inventory: true,
            billing: true
          }
        }
      }
    })

    // Get all admin users to assign them to the new workshop
    const adminUsers = await prisma.user.findMany({
      where: {
        role: UserRole.ADMIN,
        isActive: true
      }
    })

    // Create UserWorkshop relationships for all admins
    const userWorkshopPromises = adminUsers.map(admin => 
      prisma.userWorkshop.create({
        data: {
          userId: admin.id,
          workshopId: workshop.id,
          role: UserRole.ADMIN,
          isActive: true,
          assignedAt: new Date()
        }
      })
    )

    await Promise.all(userWorkshopPromises)

    // If any admin doesn't have a current workshop, set this as their current
    const adminsWithoutCurrentWorkshop = adminUsers.filter(admin => !admin.currentWorkshopId)
    
    if (adminsWithoutCurrentWorkshop.length > 0) {
      const updatePromises = adminsWithoutCurrentWorkshop.map(admin =>
        prisma.user.update({
          where: { id: admin.id },
          data: { currentWorkshopId: workshop.id }
        })
      )
      await Promise.all(updatePromises)
    }

    return NextResponse.json({
      success: true,
      workshop: {
        id: workshop.id,
        name: workshop.name,
        location: workshop.location,
        phone: workshop.phone,
        email: workshop.email,
        isActive: workshop.isActive,
        createdAt: workshop.createdAt,
        adminsAssigned: adminUsers.length
      }
    })

  } catch (error) {
    console.error('Error creating workshop:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    // Verify user is authenticated and is admin
    const session = await getServerSession({ shouldRedirect: false })
    
    if (!session || session.user.role !== UserRole.ADMIN) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin access required.' },
        { status: 403 }
      )
    }

    // Get all workshops with user count
    const workshops = await prisma.workshop.findMany({
      include: {
        users: {
          where: { isActive: true }
        },
        _count: {
          select: {
            users: {
              where: { isActive: true }
            },
            clients: true,
            vehicles: true,
            receptions: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    const workshopsWithStats = workshops.map(workshop => ({
      id: workshop.id,
      name: workshop.name,
      location: workshop.location,
      phone: workshop.phone,
      email: workshop.email,
      isActive: workshop.isActive,
      createdAt: workshop.createdAt,
      userCount: workshop._count.users,
      clientCount: workshop._count.clients,
      vehicleCount: workshop._count.vehicles,
      receptionCount: workshop._count.receptions
    }))

    return NextResponse.json({
      success: true,
      workshops: workshopsWithStats
    })

  } catch (error) {
    console.error('Error fetching workshops:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
