import db from "@/config/db";
import { CreateVehicleInput, UpdateVehicleInput } from "./vehicle.dto";
import { HttpException } from "@/lib/exceptions/HttpExceptions";

export class VehicleService {

  static async create(data: CreateVehicleInput, workshopId: string) {
    // Data is already in English format from DTO
    const vehicleData = {
      brand: data.brand,
      model: data.model,
      year: data.year,
      plates: data.plates,
      vin: data.vin,
      color: data.color,
      clientId: data.clientId,
      workshopId
    };

    // Check if a vehicle with the same plates already exists
    const existingVehicle = await db.vehicle.findUnique({
      where: { plates: vehicleData.plates }
    });

    if (existingVehicle) {
      throw HttpException.Conflict("Ya existe un vehículo registrado con estas placas");
    }

    // Check that the client exists
    const client = await db.client.findUnique({
      where: { id: vehicleData.clientId }
    });

    if (!client) {
      throw HttpException.NotFound("Cliente no encontrado");
    }

    const vehicle = await db.vehicle.create({
      data: vehicleData,
      include: {
        client: {
          select: {
            id: true,
            name: true,
            phone: true,
            clientType: true
          }
        },
        receptions: {
          include: {
            receptionist: {
              select: {
                name: true,
                email: true
              }
            }
          },
          orderBy: {
            createdAt: 'desc'
          }
        },
        _count: {
          select: {
            receptions: true
          }
        }
      }
    });

    return vehicle;
  }

  static async list(filters?: {
    clientId?: string;
    search?: string;
    enTaller?: boolean;
    workshopId?: string | null;
  }) {
    const where: any = {};

    // Filter by workshop - if null, show all (for admin global view)
    if (filters?.workshopId !== undefined) {
      if (filters.workshopId === null) {
        // Global view - no workshop filter
      } else {
        where.workshopId = filters.workshopId;
      }
    }

    if (filters?.clientId) {
      where.clientId = filters.clientId;
    }

    if (filters?.search) {
      where.OR = [
        { brand: { contains: filters.search } },
        { model: { contains: filters.search } },
        { plates: { contains: filters.search } },
        { vin: { contains: filters.search } },
        { client: { name: { contains: filters.search } } }
      ];
    }

    if (filters?.enTaller) {
      where.receptions = {
        some: {
          status: {
            in: ['RECEIVED', 'IN_DIAGNOSTIC', 'WAITING_APPROVAL', 'IN_REPAIR']
          }
        }
      };
    }

    return await db.vehicle.findMany({
      where,
      include: {
        client: {
          select: {
            id: true,
            name: true,
            phone: true,
            clientType: true
          }
        },
        receptions: {
          where: filters?.enTaller ? {
            status: {
              in: ['RECEIVED', 'IN_DIAGNOSTIC', 'WAITING_APPROVAL', 'IN_REPAIR']
            }
          } : undefined,
          include: {
            receptionist: {
              select: {
                name: true,
                email: true
              }
            }
          },
          orderBy: {
            createdAt: 'desc'
          },
          take: 1
        },
        _count: {
          select: {
            receptions: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });
  }

  static async getById(id: string) {
    const vehicle = await db.vehicle.findUnique({
      where: { id },
      include: {
        client: true,
        receptions: {
          include: {
            receptionist: {
              select: {
                name: true,
                email: true
              }
            },
            evidence: true
          },
          orderBy: {
            createdAt: 'desc'
          }
        }
      }
    });

    if (!vehicle) {
      throw HttpException.NotFound("Vehículo no encontrado");
    }

    return vehicle;
  }

  static async getByPlates(plates: string) {
    const vehicle = await db.vehicle.findUnique({
      where: { plates },
      include: {
        client: {
          select: {
            id: true,
            name: true,
            phone: true,
            clientType: true
          }
        },
        receptions: {
          include: {
            receptionist: {
              select: {
                name: true,
                email: true
              }
            }
          },
          orderBy: {
            createdAt: 'desc'
          },
          take: 3
        }
      }
    });

    return vehicle;
  }

  static async update(id: string, data: UpdateVehicleInput) {
    const existingVehicle = await db.vehicle.findUnique({
      where: { id }
    });

    if (!existingVehicle) {
      throw HttpException.NotFound("Vehículo no encontrado");
    }

    // Verificar placas únicas si se están actualizando
    if (data.plates && data.plates !== existingVehicle.plates) {
      const existingPlates = await db.vehicle.findUnique({
        where: { plates: data.plates }
      });

      if (existingPlates) {
        throw HttpException.Conflict("Ya existe un vehículo registrado con estas placas");
      }
    }

    // Verificar que el cliente existe si se está cambiando
    if (data.clientId && data.clientId !== existingVehicle.clientId) {
      const client = await db.client.findUnique({
        where: { id: data.clientId }
      });

      if (!client) {
        throw HttpException.NotFound("Cliente no encontrado");
      }
    }

    return await db.vehicle.update({
      where: { id },
      data,
      include: {
        client: {
          select: {
            id: true,
            name: true,
            phone: true,
            clientType: true
          }
        },
        _count: {
          select: {
            receptions: true
          }
        }
      }
    });
  }

  static async delete(id: string) {
    const vehicle = await db.vehicle.findUnique({
      where: { id },
      include: {
        receptions: true
      }
    });

    if (!vehicle) {
      throw HttpException.NotFound("Vehículo no encontrado");
    }

    if (vehicle.receptions.length > 0) {
      throw HttpException.BadRequest("No se puede eliminar un vehículo que tiene recepciones registradas");
    }

    return await db.vehicle.delete({
      where: { id }
    });
  }

  static async getInWorkshop() {
    return await db.vehicle.findMany({
      where: {
        receptions: {
          some: {
            status: {
              in: ['RECEIVED', 'IN_DIAGNOSTIC', 'WAITING_APPROVAL', 'IN_REPAIR']
            }
          }
        }
      },
      include: {
        client: {
          select: {
            id: true,
            name: true,
            phone: true,
            clientType: true
          }
        },
        receptions: {
          where: {
            status: {
              in: ['RECEIVED', 'IN_DIAGNOSTIC', 'WAITING_APPROVAL', 'IN_REPAIR']
            }
          },
          include: {
            receptionist: {
              select: {
                name: true,
                email: true
              }
            }
          },
          orderBy: {
            createdAt: 'desc'
          },
          take: 1
        }
      },
      orderBy: {
        receptions: {
          _count: 'desc'
        }
      }
    });
  }
}
