// Vehicle types adapted from example project to work with current Prisma schema

// Enums for vehicle specifications
export enum FuelType {
  GASOLINE = "GASOLINE",
  DIESEL = "DIESEL", 
  HYBRID = "HYBRID",
  ELECTRIC = "ELECTRIC",
  GAS = "GAS",
}

export enum TransmissionType {
  MANUAL = "MANUAL",
  AUTOMATIC = "AUTOMATIC",
  CVT = "CVT",
}

// Vehicle interface matching current Prisma schema
export interface Vehicle {
  id: string
  brand: string
  model: string
  year: number
  plates: string
  vin?: string
  color?: string

  // Additional fields from example (not in current schema but useful)
  mileage?: number
  engineType?: string
  transmission?: TransmissionType
  fuelType?: FuelType
  notes?: string

  // Relations
  clientId: string
  client?: Client

  // Metadata
  createdAt: Date
  updatedAt: Date

  // Relations (populated when needed)
  receptions?: Reception[]
}

// Vehicle creation data
export interface CreateVehicleData {
  brand: string
  model: string
  year: number
  plates: string
  vin?: string
  color?: string
  mileage?: number
  engineType?: string
  transmission?: TransmissionType
  fuelType?: FuelType
  notes?: string
  clientId: string
}

// Vehicle update data
export interface UpdateVehicleData {
  brand?: string
  model?: string
  year?: number
  plates?: string
  vin?: string
  color?: string
  mileage?: number
  engineType?: string
  transmission?: TransmissionType
  fuelType?: FuelType
  notes?: string
}

// Vehicle search/filter interface
export interface VehicleFilters {
  search?: string // Search by plates, brand, model
  clientId?: string
  brand?: string
  year?: number
  dateFrom?: Date
  dateTo?: Date
}

// Vehicle with status (for dashboard views)
export interface VehicleWithStatus extends Vehicle {
  status?: string
  entryDate?: Date
  estimatedCompletion?: Date
  currentReception?: Reception
}

// Import types that will be used in relations
import type { Client } from './client'
import type { Reception } from './reception'
