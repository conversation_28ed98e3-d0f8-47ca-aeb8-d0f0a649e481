"use client"

import { useEffect, useState } from "react"

interface HydrationBoundaryProps {
  children: React.ReactNode
  fallback?: React.ReactNode
}

/**
 * HydrationBoundary prevents hydration mismatches by only rendering
 * interactive components after the client has hydrated.
 *
 * This is useful for components that:
 * - Generate dynamic IDs (like Radix UI components)
 * - Use browser-specific APIs
 * - Have different server/client rendering
 */
export function HydrationBoundary({ children, fallback = null }: HydrationBoundaryProps) {
  const [isHydrated, setIsHydrated] = useState(false)

  useEffect(() => {
    // Set hydrated to true after the component mounts on the client
    setIsHydrated(true)
  }, [])

  // During SSR and before hydration, show fallback or nothing
  if (!isHydrated) {
    return <>{fallback}</>
  }

  // After hydration, show the actual content
  return <>{children}</>
}
