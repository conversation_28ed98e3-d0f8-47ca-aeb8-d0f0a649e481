'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON>, Controller } from 'react-hook-form'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { UserPlus, Loader2, AlertCircle, Building2 } from 'lucide-react'
import { server } from '@/app/api/server'
import { useWorkshop } from '@/contexts/workshop-context'
import toast from 'react-hot-toast'
import { useQueryClient } from '@tanstack/react-query'
import { USER_INVITATIONS, USER_WORKSHOPS_AVAILABLE } from '@/constants/queries-key'

const ROLES = [
  { value: 'RECEPCIONISTA', label: 'Recepcion<PERSON>' },
  { value: 'TECNICO', label: 'Técnico' },
  { value: 'JEFE_TALLER', label: 'Jefe de Taller' },
  { value: 'COMPRAS', label: 'Compras' },
  { value: 'FACTURACION', label: 'Facturación' },
  { value: 'ADMIN', label: 'Administrador' }
]

interface FormData {
  email: string
  role: string
  workshopIds: string[]
}

export function CreateInvitationModal() {
  const [open, setOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const { workshops, isLoadingWorkshops } = useWorkshop()
  const queryClient = useQueryClient()

  const { control, handleSubmit, reset, formState: { errors } } = useForm<FormData>({
    defaultValues: {
      email: '',
      role: 'RECEPCIONISTA',
      workshopIds: []
    }
  })

  // Initialize workshops selection based on available workshops
  useEffect(() => {
    if (workshops.length === 1) {
      // If only one workshop, preselect it
      reset({
        email: '',
        role: 'RECEPCIONISTA',
        workshopIds: [workshops[0].id]
      })
    }
  }, [workshops, reset])

  const onSubmit = async (data: FormData) => {
    setIsLoading(true)
    setError('')

    try {
      const response = await server.api.invitations.post({
        email: data.email,
        role: data.role as any,
        workshopIds: data.workshopIds
      })

      if (response.data?.success) {
        // revalidate invitations query
        queryClient.invalidateQueries({ queryKey: USER_INVITATIONS })
        toast.success('Invitación creada exitosamente')
        reset()
        setOpen(false)
      } else {
        setError(response.data?.error || 'Error al crear la invitación')
      }
    } catch (err) {
      setError('Error al crear la invitación')
    } finally {
      setIsLoading(false)
    }
  }

  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen)
    if (!newOpen) {
      // Reset form when closing
      reset()
      setError('')
    }
  }

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        <Button>
          <UserPlus className="mr-2 h-4 w-4" />
          Crear Invitación
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Nueva Invitación</DialogTitle>
          <DialogDescription>
            Invita a un nuevo usuario al sistema asignándole un rol específico.
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Email Field */}
          <div className="space-y-2">
            <Label htmlFor="email">Correo electrónico</Label>
            <Controller
              name="email"
              control={control}
              rules={{
                required: 'El email es requerido',
                pattern: {
                  value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                  message: 'Email inválido'
                }
              }}
              render={({ field }) => (
                <Input
                  {...field}
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  disabled={isLoading}
                />
              )}
            />
            {errors.email && (
              <p className="text-sm text-red-600">{errors.email.message}</p>
            )}
          </div>

          {/* Role Field */}
          <div className="space-y-2">
            <Label htmlFor="role">Rol</Label>
            <Controller
              name="role"
              control={control}
              rules={{ required: 'El rol es requerido' }}
              render={({ field }) => (
                <Select value={field.value} onValueChange={field.onChange} disabled={isLoading}>
                  <SelectTrigger>
                    <SelectValue placeholder="Selecciona un rol" />
                  </SelectTrigger>
                  <SelectContent>
                    {ROLES.map((roleOption) => (
                      <SelectItem key={roleOption.value} value={roleOption.value}>
                        {roleOption.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            />
            {errors.role && (
              <p className="text-sm text-red-600">{errors.role.message}</p>
            )}
          </div>

          {/* Workshops Field */}
          <div className="space-y-2">
            <Label>Talleres con acceso</Label>
            <Controller
              name="workshopIds"
              control={control}
              rules={{
                required: 'Debe seleccionar al menos un taller',
                validate: (value) => value.length > 0 || 'Debe seleccionar al menos un taller'
              }}
              render={({ field }) => (
                <div className="space-y-2 max-h-40 overflow-y-auto border rounded-md p-3">
                  {isLoadingWorkshops && (
                    <div className="text-sm text-gray-500 flex items-center gap-2">
                      <Loader2 className="h-4 w-4 animate-spin" />
                      Cargando talleres...
                    </div>
                  )}
                  {!isLoadingWorkshops && workshops.map((workshop) => (
                    <div key={workshop.id} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id={`workshop-${workshop.id}`}
                        checked={field.value.includes(workshop.id)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            field.onChange([...field.value, workshop.id])
                          } else {
                            field.onChange(field.value.filter(id => id !== workshop.id))
                          }
                        }}
                        disabled={isLoading}
                        className="rounded border-gray-300"
                      />
                      <Label
                        htmlFor={`workshop-${workshop.id}`}
                        className="text-sm font-normal cursor-pointer flex-1"
                      >
                        <div>
                          <div className="font-medium">{workshop.name}</div>
                          <div className="text-xs text-gray-500">{workshop.location}</div>
                        </div>
                      </Label>
                    </div>
                  ))}
                  {workshops.length === 0 && (
                    <div className="text-sm text-gray-500 flex items-center gap-2">
                      <Building2 className="h-4 w-4" />
                      No hay talleres disponibles
                    </div>
                  )}
                </div>
              )}
            />
            {errors.workshopIds && (
              <p className="text-sm text-red-600">{errors.workshopIds.message}</p>
            )}
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => setOpen(false)}
              disabled={isLoading}
            >
              Cancelar
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creando...
                </>
              ) : (
                'Crear Invitación'
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
