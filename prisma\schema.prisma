// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  // output   = "../src/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id            String    @id
  name          String
  email         String
  emailVerified Boolean
  image         String?
  role          UserRole  @default(RECEPCIONISTA)
  isActive      Boolean   @default(true)
  createdAt     DateTime
  updatedAt     DateTime
  sessions      Session[]
  accounts      Account[]

  // Workshop relations
  workshopAccess  UserWorkshop[]  // Many-to-many relation with workshops
  currentWorkshopId String?       // Current active workshop
  currentWorkshop   Workshop?     @relation("UserCurrentWorkshop", fields: [currentWorkshopId], references: [id])

  // Relations for the workshop system
  receptions      Reception[]
  clientsCreated  Client[] @relation("ClientCreatedBy")
  invitationsSent Invitation[] @relation("InvitationSentBy")

  @@unique([email])
  @@map("user")
}

enum UserRole {
  ADMIN
  RECEPCIONISTA
  TECNICO
  JEFE_TALLER
  COMPRAS
  FACTURACION
}

model Session {
  id        String   @id
  expiresAt DateTime
  token     String
  createdAt DateTime
  updatedAt DateTime
  ipAddress String?
  userAgent String?
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([token])
  @@map("session")
}

model Account {
  id                    String    @id
  accountId             String
  providerId            String
  userId                String
  user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  accessToken           String?
  refreshToken          String?
  idToken               String?
  accessTokenExpiresAt  DateTime?
  refreshTokenExpiresAt DateTime?
  scope                 String?
  password              String?
  createdAt             DateTime
  updatedAt             DateTime

  @@map("account")
}

model Verification {
  id         String    @id
  identifier String
  value      String
  expiresAt  DateTime
  createdAt  DateTime?
  updatedAt  DateTime?

  @@map("verification")
}

model Invitation {
  id          String           @id @default(cuid())
  email       String
  role        UserRole         @default(RECEPCIONISTA)
  token       String           @unique
  expiresAt   DateTime
  status      InvitationStatus @default(PENDING)
  workshopIds String[]         // Array of workshop IDs the user will have access to

  // Relaciones
  sentById    String
  sentBy      User             @relation("InvitationSentBy", fields: [sentById], references: [id])

  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt

  @@unique([email])
  @@map("invitation")
}

enum InvitationStatus {
  PENDING
  ACCEPTED
  EXPIRED
  CANCELLED
}

model Client {
  id              String      @id @default(cuid())
  name            String
  phone           String
  email           String?
  clientType      ClientType  @default(INDIVIDUAL)

  // Tax information
  businessName    String?
  taxId           String?
  address         String?

  // Fleet configuration
  paymentTerms    String?     // For fleet clients
  billingPeriod   String?     // weekly/biweekly/monthly

  // Workshop relation
  workshopId      String
  workshop        Workshop    @relation(fields: [workshopId], references: [id])

  // Metadata
  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @updatedAt
  createdById     String
  createdBy       User        @relation("ClientCreatedBy", fields: [createdById], references: [id])

  // Relations
  vehicles        Vehicle[]
  receptions      Reception[]

  @@map("client")
}

enum ClientType {
  INDIVIDUAL
  FLEET
}

model Vehicle {
  id          String    @id @default(cuid())
  brand       String
  model       String
  year        Int
  plates      String    @unique
  vin         String?
  color       String?

  // Relations
  clientId    String
  client      Client    @relation(fields: [clientId], references: [id])
  workshopId  String
  workshop    Workshop  @relation(fields: [workshopId], references: [id])
  receptions  Reception[]

  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  @@map("vehicle")
}

model Reception {
  id              String            @id @default(cuid())

  // Basic information
  entryDate       DateTime          @default(now())
  mileage         Int
  serviceType     ServiceType
  otherService    String?           // If "Other" is selected
  observations    String?
  tiresCondition  TiresCondition?

  // Assigned technician
  assignedTech    String?

  // Relations
  clientId        String
  client          Client            @relation(fields: [clientId], references: [id])
  vehicleId       String
  vehicle         Vehicle           @relation(fields: [vehicleId], references: [id])
  workshopId      String
  workshop        Workshop          @relation(fields: [workshopId], references: [id])
  receptionistId  String
  receptionist    User              @relation(fields: [receptionistId], references: [id])

  // Photo evidence
  evidence        PhotoEvidence[]

  // Status
  status          ReceptionStatus   @default(RECEIVED)

  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt

  @@map("reception")
}

enum ServiceType {
  MINOR_PREVENTIVE
  MAJOR_PREVENTIVE
  SPARK_PLUG_CHANGE
  DIAGNOSTIC
  BODYWORK_PAINT
  OTHER
}

enum TiresCondition {
  GOOD_APPEARANCE
  SOME_DETAILS
}

enum ReceptionStatus {
  RECEIVED
  IN_DIAGNOSTIC
  WAITING_APPROVAL
  WAITING_PARTS
  IN_REPAIR
  COMPLETED
  DELIVERED
}

model PhotoEvidence {
  id          String        @id @default(cuid())
  url         String        // URL of the file in storage
  type        EvidenceType
  description String?

  receptionId String
  reception   Reception     @relation(fields: [receptionId], references: [id], onDelete: Cascade)

  createdAt   DateTime      @default(now())

  @@map("photo_evidence")
}

enum EvidenceType {
  VIN
  DASHBOARD
  FRONT
  LEFT_FRONT_SIDE
  DRIVER_SEAT
  LEFT_REAR_SIDE
  LEFT_REAR_SEAT
  REAR
  TRUNK
  SPARE_TIRE
  RIGHT_REAR_SIDE
  RIGHT_REAR_SEAT
  RIGHT_FRONT_SIDE
  PASSENGER_SEAT
  EXTERIOR_DAMAGE
  ENGINE_BAY
}

// Workshop Management Models
model Workshop {
  id          String    @id @default(cuid())
  name        String
  location    String
  phone       String?
  email       String?
  isActive    Boolean   @default(true)

  // Configuration
  settings    Json?     // Store workshop-specific settings

  // Relations
  users       UserWorkshop[]  // Many-to-many relation with users
  currentUsers User[]    @relation("UserCurrentWorkshop")
  receptions  Reception[]
  clients     Client[]
  vehicles    Vehicle[]

  // Metadata
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  @@map("workshop")
}

// Junction table for User-Workshop many-to-many relationship
model UserWorkshop {
  id          String    @id @default(cuid())
  userId      String
  workshopId  String
  role        UserRole  // Role in this specific workshop
  isActive    Boolean   @default(true)

  // Relations
  user        User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  workshop    Workshop  @relation(fields: [workshopId], references: [id], onDelete: Cascade)

  // Metadata
  assignedAt  DateTime  @default(now())
  assignedBy  String?   // ID of admin who assigned this user

  @@unique([userId, workshopId])
  @@map("user_workshop")
}
