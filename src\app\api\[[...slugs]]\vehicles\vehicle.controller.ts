import { Elysia, t } from 'elysia'
import { VehicleService } from './vehicle.service'
import { CreateVehicleSchema, UpdateVehicleSchema } from './vehicle.dto';
import { authMiddleware } from '../auth/auth.middleware'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export const vehicleController = new Elysia({ prefix: '/vehicles' })
  .use(authMiddleware)
  
  .get('/', async ({ query, user }) => {
    // Get user's current workshop
    const userWithWorkshop = await prisma.user.findUnique({
      where: { id: user.id },
      select: { currentWorkshopId: true, role: true }
    })

    // For admin users, if no workshop is selected, show global view
    const workshopId = userWithWorkshop?.role === 'ADMIN' && !userWithWorkshop?.currentWorkshopId
      ? null
      : userWithWorkshop?.currentWorkshopId

    const vehicles = await VehicleService.list({
      clientId: query.clientId,
      search: query.search,
      enTaller: query.enTaller === 'true',
      workshopId
    });

    return {
      success: true,
      data: vehicles
    };
  }, {
    query: t.Object({
      clientId: t.Optional(t.String()),
      search: t.Optional(t.String()),
      enTaller: t.Optional(t.String())
    })
  })
  
  .post('/', async ({ body, user }) => {
    // Get user's current workshop
    const userWithWorkshop = await prisma.user.findUnique({
      where: { id: user.id },
      select: { currentWorkshopId: true }
    })

    if (!userWithWorkshop?.currentWorkshopId) {
      return {
        success: false,
        error: "No workshop selected. Please select a workshop first."
      }
    }

    const vehicle = await VehicleService.create(body, userWithWorkshop.currentWorkshopId);

    return {
      success: true,
      data: vehicle,
      message: "Vehículo registrado exitosamente"
    };
  }, {
    body: CreateVehicleSchema
  })
  
  .get('/plates/:plates', async ({ params }) => {
    const vehicle = await VehicleService.getByPlates(params.plates);

    return {
      success: true,
      data: vehicle
    };
  }, {
    params: t.Object({
      plates: t.String()
    })
  })
  
  .get('/en-taller', async () => {
    const vehicles = await VehicleService.getInWorkshop();

    return {
      success: true,
      data: vehicles
    };
  })

  .get('/:id', async ({ params }) => {
    const vehicle = await VehicleService.getById(params.id);

    return {
      success: true,
      data: vehicle
    };
  }, {
    params: t.Object({
      id: t.String()
    })
  })

  .put('/:id', async ({ params, body }) => {
    const vehicle = await VehicleService.update(params.id, body);

    return {
      success: true,
      data: vehicle,
      message: "Vehículo actualizado exitosamente"
    };
  }, {
    params: t.Object({
      id: t.String()
    }),
    body: UpdateVehicleSchema
  })

  .delete('/:id', async ({ params }) => {
    await VehicleService.delete(params.id);

    return {
      success: true,
      message: "Vehículo eliminado exitosamente"
    };
  }, {
    params: t.Object({
      id: t.String()
    })
  })
