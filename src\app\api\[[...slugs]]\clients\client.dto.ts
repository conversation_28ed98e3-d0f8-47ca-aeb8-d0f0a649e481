import { t } from 'elysia'

export const CreateClientSchema = t.Object({
  name: t.String({ minLength: 2, maxLength: 100 }),
  phone: t.String({ minLength: 10, maxLength: 15 }),
  email: t.Optional(t.String({ format: 'email' })),
  clientType: t.Union([
    t.Literal('INDIVIDUAL'),
    t.Literal('FLEET')
  ]),
  // Tax information (optional)
  businessName: t.Optional(t.String()),
  taxId: t.Optional(t.String()),
  address: t.Optional(t.String()),
  // Fleet configuration (only for fleet clients)
  paymentTerms: t.Optional(t.String()),
  billingPeriod: t.Optional(t.String())
})

export const UpdateClientSchema = t.Object({
  name: t.Optional(t.String({ minLength: 2, maxLength: 100 })),
  phone: t.Optional(t.String({ minLength: 10, maxLength: 15 })),
  email: t.Optional(t.String({ format: 'email' })),
  clientType: t.Optional(t.Union([
    t.Literal('INDIVIDUAL'),
    t.Literal('FLEET')
  ])),
  businessName: t.Optional(t.String()),
  taxId: t.Optional(t.String()),
  address: t.Optional(t.String()),
  paymentTerms: t.Optional(t.String()),
  billingPeriod: t.Optional(t.String())
})

export const ClientResponseSchema = t.Object({
  id: t.String(),
  name: t.String(),
  phone: t.String(),
  email: t.Optional(t.String()),
  clientType: t.String(),
  businessName: t.Optional(t.String()),
  taxId: t.Optional(t.String()),
  address: t.Optional(t.String()),
  paymentTerms: t.Optional(t.String()),
  billingPeriod: t.Optional(t.String()),
  createdAt: t.Date(),
  updatedAt: t.Date()
})

export type CreateClientInput = typeof CreateClientSchema.static
export type UpdateClientInput = typeof UpdateClientSchema.static
export type ClientResponse = typeof ClientResponseSchema.static
