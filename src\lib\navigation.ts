import { UserRole } from "@/types"
import {
  Car,
  Users,
  Package,
  <PERSON>ch,
  BarChart3,
  ClipboardList,
  DollarSign,
  <PERSON>tings,
  UserCheck,
  Truck,
  Home,
  Shield,
} from "lucide-react"

export interface NavigationItem {
  name: string
  href: string
  icon: any
  roles: UserRole[]
  current?: boolean
  description?: string
}

export const navigationItems: NavigationItem[] = [
  {
    name: "Dashboard",
    href: "/dashboard",
    icon: Home,
    description: "Vista general del taller",
    roles: [
      UserRole.ADMIN,
      UserRole.RECEPCIONISTA,
      UserRole.TECNICO,
      UserRole.JEFE_TALLER,
      UserRole.COMPRAS,
      UserRole.FACTURACION,
    ],
  },
  {
    name: "<PERSON>ce<PERSON><PERSON>",
    href: "/recepcion",
    icon: ClipboardList,
    description: "Recepción de vehículos",
    roles: [UserRole.ADMIN, UserRole.RECEPCIONISTA, UserRole.JEFE_TALLER],
  },
  {
    name: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
    href: "/vehiculos",
    icon: Car,
    description: "Gestión de vehículos",
    roles: [UserRole.ADMIN, UserRole.RECEPCIONISTA, UserRole.TECNICO, UserRole.JEFE_TALLER],
  },
  {
    name: "Clientes",
    href: "/clientes",
    icon: Users,
    description: "Gestión de clientes",
    roles: [UserRole.ADMIN, UserRole.RECEPCIONISTA, UserRole.JEFE_TALLER, UserRole.FACTURACION],
  },
  {
    name: "Inventario",
    href: "/inventario",
    icon: Package,
    description: "Control de inventario",
    roles: [UserRole.ADMIN, UserRole.COMPRAS, UserRole.JEFE_TALLER, UserRole.TECNICO],
  },
  {
    name: "Técnicos",
    href: "/tecnicos",
    icon: Wrench,
    description: "Gestión de técnicos",
    roles: [UserRole.ADMIN, UserRole.JEFE_TALLER],
  },
  {
    name: "Órdenes de Trabajo",
    href: "/ordenes-trabajo",
    icon: UserCheck,
    description: "Órdenes de trabajo",
    roles: [UserRole.ADMIN, UserRole.TECNICO, UserRole.JEFE_TALLER],
  },
  {
    name: "Compras",
    href: "/compras",
    icon: Truck,
    description: "Gestión de compras",
    roles: [UserRole.ADMIN, UserRole.COMPRAS, UserRole.JEFE_TALLER],
  },
  {
    name: "Facturación",
    href: "/facturacion",
    icon: DollarSign,
    description: "Facturación y cobranza",
    roles: [UserRole.ADMIN, UserRole.FACTURACION, UserRole.JEFE_TALLER],
  },
  {
    name: "Reportes",
    href: "/reportes",
    icon: BarChart3,
    description: "Reportes y estadísticas",
    roles: [UserRole.ADMIN, UserRole.JEFE_TALLER, UserRole.FACTURACION],
  },
  {
    name: "Administración",
    href: "/admin",
    icon: Shield,
    description: "Configuración del sistema",
    roles: [UserRole.ADMIN],
  },
  {
    name: "Configuración",
    href: "/configuracion",
    icon: Settings,
    description: "Configuración general",
    roles: [UserRole.ADMIN],
  },
]

export function getNavigationForRole(role: UserRole): NavigationItem[] {
  return navigationItems.filter((item) => item.roles.includes(role))
}

// Get navigation items with current page marked
export function getNavigationWithCurrent(role: UserRole, currentPath: string): NavigationItem[] {
  return getNavigationForRole(role).map(item => ({
    ...item,
    current: currentPath.startsWith(item.href)
  }))
}

// Check if user has access to a specific route
export function hasRouteAccess(role: UserRole, path: string): boolean {
  const item = navigationItems.find(item => path.startsWith(item.href))
  return item ? item.roles.includes(role) : false
}

// Get main navigation sections
export function getNavigationSections(role: UserRole) {
  const items = getNavigationForRole(role)
  
  return {
    main: items.filter(item => [
      "/dashboard",
      "/recepcion", 
      "/vehiculos",
      "/clientes"
    ].includes(item.href)),
    
    operations: items.filter(item => [
      "/inventario",
      "/tecnicos",
      "/ordenes-trabajo",
      "/compras"
    ].includes(item.href)),
    
    business: items.filter(item => [
      "/facturacion",
      "/reportes"
    ].includes(item.href)),
    
    admin: items.filter(item => [
      "/admin",
      "/configuracion"
    ].includes(item.href))
  }
}
