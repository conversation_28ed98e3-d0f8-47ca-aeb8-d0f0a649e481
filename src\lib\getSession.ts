import { cache } from "react";
import { headers } from "next/headers";
import { auth } from "./auth";
import { redirect } from "next/navigation";
import { Session, User } from "@prisma/client";
import db from "@/config/db";

async function ensureDefaultWorkshop(userId: string) {
    try {
        // Check if there are any workshops
        const workshopCount = await db.workshop.count();

        if (workshopCount === 0) {
            console.log('No workshops found, creating default workshop...');

            // Create default workshop
            const defaultWorkshop = await db.workshop.create({
                data: {
                    name: 'Taller Principal',
                    location: 'Ubicación Principal',
                    phone: '+52 55 0000 0000',
                    email: '<EMAIL>',
                    isActive: true,
                    settings: {
                        businessHours: {
                            monday: { open: '08:00', close: '18:00' },
                            tuesday: { open: '08:00', close: '18:00' },
                            wednesday: { open: '08:00', close: '18:00' },
                            thursday: { open: '08:00', close: '18:00' },
                            friday: { open: '08:00', close: '18:00' },
                            saturday: { open: '08:00', close: '14:00' },
                            sunday: { closed: true }
                        },
                        currency: 'MXN',
                        timezone: 'America/Mexico_City',
                        features: {
                            photoEvidence: true,
                            diagnostics: true,
                            inventory: true,
                            billing: true
                        }
                    }
                }
            });

            console.log('Default workshop created:', defaultWorkshop.id);

            // Assign current user to the workshop as ADMIN
            await db.userWorkshop.create({
                data: {
                    userId: userId,
                    workshopId: defaultWorkshop.id,
                    role: 'ADMIN',
                    isActive: true,
                    assignedAt: new Date()
                }
            });

            // Set as current workshop for the user
            await db.user.update({
                where: { id: userId },
                data: { currentWorkshopId: defaultWorkshop.id }
            });

            console.log('User assigned to default workshop as ADMIN');
        }
    } catch (error) {
        console.error('Error ensuring default workshop:', error);
    }
}

export const getServerSession = cache(async (
    { shouldRedirect = true }:
    { shouldRedirect?: boolean } = { shouldRedirect: true }
) => {

    const session = await auth.api.getSession({
        headers: await headers(),
    }) as { user: User; session: Session } | null;

    // if (session) {
    //     // Ensure default workshop exists and user is assigned
    //     await ensureDefaultWorkshop(session.user.id);
    // }

    if (!session && shouldRedirect) {
        return redirect("/login");
    }

    return session!

  }
);