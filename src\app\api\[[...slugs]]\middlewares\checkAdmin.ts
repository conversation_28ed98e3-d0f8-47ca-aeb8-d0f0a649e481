import { HttpException } from '@/lib/exceptions/HttpExceptions';
import { UserRole } from '@/types';
import { Elysia } from 'elysia';

export const checkAdminMiddleware = (app: Elysia) =>
    app
        .derive(({ user }: any) => {
            if (user.role !== UserRole.ADMIN) {
                throw HttpException.Forbidden('Admin access required');
            }
            return { user };
        });
