import { Elysia, t } from 'elysia'
import { ClientService } from './client.service'
import { CreateClientSchema, UpdateClientSchema } from './client.dto'
import { authMiddleware } from '../auth/auth.middleware'
import { HttpException } from '@/lib/exceptions/HttpExceptions'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export const clientController = new Elysia({ prefix: '/clients' })
  .use(authMiddleware)
  .get('/', async ({ query, user }) => {
      // Get user's current workshop
      const userWithWorkshop = await prisma.user.findUnique({
        where: { id: user.id },
        select: { currentWorkshopId: true, role: true }
      })

      // For admin users, if no workshop is selected, show global view
      const workshopId = userWithWorkshop?.role === 'ADMIN' && !userWithWorkshop?.currentWorkshopId
        ? null
        : userWithWorkshop?.currentWorkshopId

      const clients = await ClientService.list({
        clientType: query.clientType as any,
        search: query.search,
        workshopId
      });

      return {
        success: true,
        data: clients
      };

  }, {
    query: t.Object({
      clientType: t.Optional(t.Union([
        t.Literal('INDIVIDUAL'),
        t.Literal('FLEET')
      ])),
      search: t.Optional(t.String())
    })
  })
  
  .post('/', async ({ body, user }) => {
      console.log('POST /clients - Body:', body)
      console.log('POST /clients - User:', user)

      // Get user's current workshop
      const userWithWorkshop = await prisma.user.findUnique({
        where: { id: user.id },
        select: { currentWorkshopId: true }
      })

      if (!userWithWorkshop?.currentWorkshopId) {
        // return {
        //   success: false,
        //   error: "No workshop selected. Please select a workshop first."
        // }
        throw HttpException.BadRequest("No workshop selected. Please select a workshop first.")
      }

      const client = await ClientService.create(body, user.id, userWithWorkshop.currentWorkshopId);
      console.log('Client created:', client)

      return {
        success: true,
        data: client,
        message: "Client created successfully"
      };

  }, {
    body: CreateClientSchema
  })

  .get('/:id', async ({ params }) => {
      const client = await ClientService.getById(params.id);

      return {
        success: true,
        data: client
      };
  }, {
    params: t.Object({
      id: t.String()
    })
  })

  .put('/:id', async ({ params, body }) => {
      const client = await ClientService.update(params.id, body);

      return {
        success: true,
        data: client,
        message: "Client updated successfully"
      };
  }, {
    params: t.Object({
      id: t.String()
    }),
    body: UpdateClientSchema
  })
  
  .delete('/:id', async ({ params }) => {
      await ClientService.delete(params.id);

      return {
        success: true,
        message: "Client deleted successfully"
      };
  }, {
    params: t.Object({
      id: t.String()
    })
  })

  .get('/stats/general', async () => {
      const stats = await ClientService.getStats();

      return {
        success: true,
        data: stats
      };
 
  })
