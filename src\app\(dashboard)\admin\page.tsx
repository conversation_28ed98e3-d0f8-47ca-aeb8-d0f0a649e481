"use client"

import { useRole, useIsAdmin } from '@/hooks/use-role'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Building2,
  Users,
  Settings,
  Plus,
  BarChart3,
  Shield,
  UserCheck,
  Wrench
} from 'lucide-react'
import Link from 'next/link'

export default function AdminDashboard() {
  const { user, currentRole } = useRole()
  const isAdmin = useIsAdmin()

  // Redirect if not admin
  if (!isAdmin) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5 text-destructive" />
              Acceso Denegado
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-4">
              No tienes permisos para acceder al panel de administración.
            </p>
            <Button asChild>
              <Link href="/dashboard">
                Volver al Dashboard
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Mock data for admin stats - replace with real API calls
  const adminStats = {
    totalWorkshops: 1,
    totalUsers: 3,
    activeUsers: 3,
    pendingInvitations: 0
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">
            Panel de Administración
          </h1>
          <p className="text-muted-foreground mt-1">
            Gestiona talleres, usuarios y configuración del sistema
          </p>
        </div>

        <div className="flex gap-2">
          <Button asChild>
            <Link href="/admin/talleres/nuevo">
              <Plus className="w-4 h-4 mr-2" />
              Nuevo Taller
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link href="/admin/usuarios/invitar">
              <UserCheck className="w-4 h-4 mr-2" />
              Invitar Usuario
            </Link>
          </Button>
        </div>
      </div>

      {/* Admin Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Talleres</CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{adminStats.totalWorkshops}</div>
            <p className="text-xs text-muted-foreground">
              Talleres registrados
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Usuarios</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{adminStats.totalUsers}</div>
            <p className="text-xs text-muted-foreground">
              Usuarios registrados
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Usuarios Activos</CardTitle>
            <UserCheck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{adminStats.activeUsers}</div>
            <p className="text-xs text-muted-foreground">
              Usuarios activos
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Invitaciones</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{adminStats.pendingInvitations}</div>
            <p className="text-xs text-muted-foreground">
              Pendientes
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Admin Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Workshop Management */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building2 className="h-5 w-5" />
              Gestión de Talleres
            </CardTitle>
            <CardDescription>
              Administra talleres, configuraciones y ubicaciones
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 gap-2">
              <Button asChild variant="outline" className="justify-start">
                <Link href="/admin/talleres">
                  <Building2 className="w-4 h-4 mr-2" />
                  Ver Todos los Talleres
                </Link>
              </Button>
              <Button asChild variant="outline" className="justify-start">
                <Link href="/admin/talleres/nuevo">
                  <Plus className="w-4 h-4 mr-2" />
                  Crear Nuevo Taller
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* User Management */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Gestión de Usuarios
            </CardTitle>
            <CardDescription>
              Administra usuarios, roles y permisos
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 gap-2">
              <Button asChild variant="outline" className="justify-start">
                <Link href="/admin/usuarios">
                  <Users className="w-4 h-4 mr-2" />
                  Ver Todos los Usuarios
                </Link>
              </Button>
              <Button asChild variant="outline" className="justify-start">
                <Link href="/admin/invitaciones">
                  <UserCheck className="w-4 h-4 mr-2" />
                  Gestionar Invitaciones
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* System Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Resumen del Sistema
          </CardTitle>
          <CardDescription>
            Vista general del estado del sistema
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 border rounded-lg">
              <Building2 className="h-8 w-8 mx-auto mb-2 text-primary" />
              <h3 className="font-semibold">Taller Ruedda</h3>
              <p className="text-sm text-muted-foreground">Taller Principal</p>
              <Badge variant="default" className="mt-2">Activo</Badge>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <Users className="h-8 w-8 mx-auto mb-2 text-blue-500" />
              <h3 className="font-semibold">3 Usuarios</h3>
              <p className="text-sm text-muted-foreground">Admin, Recepcionista, Técnico</p>
              <Badge variant="secondary" className="mt-2">Todos Activos</Badge>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <Wrench className="h-8 w-8 mx-auto mb-2 text-green-500" />
              <h3 className="font-semibold">Sistema</h3>
              <p className="text-sm text-muted-foreground">Funcionando correctamente</p>
              <Badge variant="default" className="mt-2 bg-green-100 text-green-800">Operativo</Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
