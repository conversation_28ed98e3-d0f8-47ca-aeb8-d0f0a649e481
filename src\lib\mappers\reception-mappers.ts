// Funciones de mapeo para mostrar valores legibles en español en el frontend

export const getServiceTypeLabel = (type: string): string => {
  const mapping: Record<string, string> = {
    'MINOR_PREVENTIVE': 'Servicio Preventivo Menor',
    'MAJOR_PREVENTIVE': 'Servicio Preventivo Mayor',
    'SPARK_PLUG_CHANGE': 'Cambio de Bujías',
    'DIAGNOSTIC': 'Diagnóstico',
    'BODYWORK_PAINT': 'Hojalatería y Pintura',
    'OTHER': 'Otro'
  }
  return mapping[type] || type
}

export const getStatusLabel = (status: string): string => {
  const mapping: Record<string, string> = {
    'RECEIVED': 'Recibido',
    'IN_DIAGNOSTIC': 'En Diagnóstico',
    'WAITING_APPROVAL': 'Esperando Aprobación',
    'WAITING_PARTS': 'Esperando Refacciones',
    'IN_REPAIR': 'En Reparación',
    'COMPLETED': 'Terminado',
    'DELIVERED': 'Entregado'
  }
  return mapping[status] || status
}

export const getTiresConditionLabel = (condition: string): string => {
  const mapping: Record<string, string> = {
    'GOOD_APPEARANCE': 'Buena Apariencia',
    'SOME_DETAILS': 'Detalle en Algunas'
  }
  return mapping[condition] || condition
}

export const getEvidenceTypeLabel = (type: string): string => {
  const mapping: Record<string, string> = {
    'VIN': 'Foto de VIN',
    'DASHBOARD': 'Foto del tablero',
    'FRONT': 'Foto de parte frontal',
    'LEFT_FRONT_SIDE': 'Foto lateral izquierdo (parte delantera)',
    'DRIVER_SEAT': 'Foto de asiento de piloto',
    'LEFT_REAR_SIDE': 'Foto lateral izquierdo (parte trasera)',
    'LEFT_REAR_SEAT': 'Foto de asiento trasero izquierdo',
    'REAR': 'Foto trasera',
    'TRUNK': 'Foto de cajuela',
    'SPARE_TIRE': 'Foto de llanta de refacción',
    'RIGHT_REAR_SIDE': 'Foto lateral derecho (parte trasera)',
    'RIGHT_REAR_SEAT': 'Foto de asiento trasero derecho',
    'RIGHT_FRONT_SIDE': 'Foto lateral derecho (parte delantera)',
    'PASSENGER_SEAT': 'Foto de asiento de copiloto',
    'EXTERIOR_DAMAGE': 'Foto de daños exteriores',
    'ENGINE_BAY': 'Foto de bahía del motor'
  }
  return mapping[type] || type
}

// Función para obtener el color del badge según el status
export const getStatusBadgeVariant = (status: string): 'default' | 'secondary' | 'destructive' | 'outline' => {
  const mapping: Record<string, 'default' | 'secondary' | 'destructive' | 'outline'> = {
    'RECEIVED': 'secondary',
    'IN_DIAGNOSTIC': 'default',
    'WAITING_APPROVAL': 'outline',
    'WAITING_PARTS': 'outline',
    'IN_REPAIR': 'default',
    'COMPLETED': 'secondary',
    'DELIVERED': 'destructive'
  }
  return mapping[status] || 'default'
}
