import { treaty } from '@elysiajs/eden'
import type { App } from "./[[...slugs]]/route";

const baseUrl = process.env.NODE_ENV === 'production'
  ? 'https://ruedda.vercel.app'
  : 'http://localhost:3000'

export const server = treaty<App>(baseUrl, {
  fetch: {
    credentials: 'include',
  }
})


/* 
    Example of usage in client side as trpc client:

    */
//    server.api.users.get()
//    server.api.users.clone({ id: '1'} ).get({ query: { name: '<PERSON>', other: '<PERSON><PERSON>' } })
