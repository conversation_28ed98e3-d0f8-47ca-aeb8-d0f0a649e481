"use client"

import { useState, useEffect } from "react"
import { useQuery } from "@tanstack/react-query"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Search, User, Building, X } from "lucide-react"
import { type Client, ClientType } from "@/types"
import { server } from "@/app/api/server"

interface ClientSearchProps {
  onClientSelect: (client: Client | null) => void
  selectedClient: Client | null
  disabled?: boolean
}

export function ClientSearch({ onClientSelect, selectedClient, disabled = false }: ClientSearchProps) {
  const [searchTerm, setSearchTerm] = useState("")
  const [showResults, setShowResults] = useState(false)

  // Query for searching clients
  const { data: searchResults, isLoading } = useQuery({
    queryKey: ['clients-search', searchTerm],
    queryFn: async () => {
      if (!searchTerm.trim()) return []
      
      try {
        const response = await server.api.clients.get({
          query: {
            search: searchTerm,
            limit: '10'
          }
        })
        
        if (response.error) {
          console.error('Error searching clients:', response.error)
          return []
        }
        
        return response.data?.data || []
      } catch (error) {
        console.error('Error in client search:', error)
        return []
      }
    },
    enabled: searchTerm.length >= 2,
    staleTime: 30000, // 30 seconds
  })

  const handleSearch = (value: string) => {
    setSearchTerm(value)
    setShowResults(value.length >= 2)
  }

  const handleSelectClient = (client: Client) => {
    onClientSelect(client)
    setShowResults(false)
    setSearchTerm("")
  }

  const handleClearSelection = () => {
    onClientSelect(null)
    setSearchTerm("")
  }

  // Close results when clicking outside
  useEffect(() => {
    const handleClickOutside = () => {
      setShowResults(false)
    }

    if (showResults) {
      document.addEventListener('click', handleClickOutside)
      return () => document.removeEventListener('click', handleClickOutside)
    }
  }, [showResults])

  if (selectedClient) {
    return (
      <div className="space-y-2">
        <div className="flex items-center justify-between p-3 border rounded-lg bg-muted/50">
          <div className="flex items-center gap-3">
            {selectedClient.clientType === ClientType.INDIVIDUAL ? (
              <User className="h-5 w-5 text-muted-foreground" />
            ) : (
              <Building className="h-5 w-5 text-muted-foreground" />
            )}
            <div>
              <p className="font-medium">{selectedClient.name}</p>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <span>{selectedClient.phone}</span>
                {selectedClient.email && (
                  <>
                    <span>•</span>
                    <span>{selectedClient.email}</span>
                  </>
                )}
              </div>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant={selectedClient.clientType === ClientType.FLEET ? "default" : "secondary"}>
              {selectedClient.clientType === ClientType.FLEET ? "Flotillero" : "Individual"}
            </Badge>
            {!disabled && (
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={handleClearSelection}
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="relative space-y-2" onClick={(e) => e.stopPropagation()}>
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Buscar cliente por nombre, teléfono o email..."
          value={searchTerm}
          onChange={(e) => handleSearch(e.target.value)}
          className="pl-10"
          disabled={disabled}
        />
      </div>

      {showResults && (
        <Card className="absolute top-full left-0 right-0 z-50 max-h-60 overflow-y-auto">
          <CardContent className="p-2">
            {isLoading ? (
              <div className="flex items-center justify-center p-4">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
              </div>
            ) : searchResults && searchResults.length > 0 ? (
              <div className="space-y-1">
                {searchResults.map((client: Client) => (
                  <div
                    key={client.id}
                    className="flex items-center gap-3 p-2 rounded-lg hover:bg-muted cursor-pointer transition-colors"
                    onClick={() => handleSelectClient(client)}
                  >
                    {client.clientType === ClientType.INDIVIDUAL ? (
                      <User className="h-4 w-4 text-muted-foreground" />
                    ) : (
                      <Building className="h-4 w-4 text-muted-foreground" />
                    )}
                    <div className="flex-1">
                      <p className="font-medium text-sm">{client.name}</p>
                      <div className="flex items-center gap-2 text-xs text-muted-foreground">
                        <span>{client.phone}</span>
                        {client.email && (
                          <>
                            <span>•</span>
                            <span>{client.email}</span>
                          </>
                        )}
                      </div>
                    </div>
                    <Badge 
                      variant={client.clientType === ClientType.FLEET ? "default" : "secondary"}
                      className="text-xs"
                    >
                      {client.clientType === ClientType.FLEET ? "Flotillero" : "Individual"}
                    </Badge>
                  </div>
                ))}
              </div>
            ) : searchTerm.length >= 2 ? (
              <div className="text-center p-4 text-muted-foreground">
                <p className="text-sm">No se encontraron clientes</p>
                <p className="text-xs mt-1">Intenta con otro término de búsqueda</p>
              </div>
            ) : null}
          </CardContent>
        </Card>
      )}

      {searchTerm.length > 0 && searchTerm.length < 2 && (
        <p className="text-xs text-muted-foreground">
          Escribe al menos 2 caracteres para buscar
        </p>
      )}
    </div>
  )
}
