{"name": "talleres", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "create-admin": "bun run scripts/create-admin.ts"}, "prisma": {"seed": "bun run prisma/seed.ts"}, "dependencies": {"@elysiajs/eden": "^1.3.2", "@hookform/resolvers": "^3.10.0", "@prisma/client": "^6.14.0", "@radix-ui/react-accordion": "^1.2.12", "@radix-ui/react-alert-dialog": "^1.1.15", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-toast": "^1.2.15", "@tanstack/react-query": "^5.85.0", "@tanstack/react-table": "^8.21.3", "@uploadthing/react": "^7.2.0", "better-auth": "^1.3.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "elysia": "^1.2.14", "lucide-react": "^0.539.0", "next": "15.4.6", "prisma": "^6.13.0", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.62.0", "react-hot-toast": "^2.5.2", "recharts": "^3.1.2", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1", "uploadthing": "^7.4.0", "zod": "^3.24.1"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@tanstack/react-query-devtools": "^5.85.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "tailwindcss": "^4", "tw-animate-css": "^1.3.6", "typescript": "^5"}}