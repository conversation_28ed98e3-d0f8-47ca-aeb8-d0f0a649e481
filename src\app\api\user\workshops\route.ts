import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'
import { getServerSession } from '@/lib/getSession'

const prisma = new PrismaClient()

export async function GET(request: NextRequest) {
  try {
    // Verify user is authenticated
    const session = await getServerSession({ shouldRedirect: false })
    
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Get user's workshops
    const userWorkshops = await prisma.userWorkshop.findMany({
      where: {
        userId: session.user.id,
        isActive: true
      },
      include: {
        workshop: true
      },
      orderBy: {
        assignedAt: 'asc'
      }
    })

    const workshops = userWorkshops.map(uw => ({
      id: uw.workshop.id,
      name: uw.workshop.name,
      location: uw.workshop.location,
      isActive: uw.workshop.isActive,
      role: uw.role
    }))

    return NextResponse.json({
      success: true,
      workshops
    })

  } catch (error) {
    console.error('Error fetching user workshops:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
