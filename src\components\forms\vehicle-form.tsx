"use client"

import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle, Car } from "lucide-react"
import { FuelType, TransmissionType, type CreateVehicleData, type Client } from "@/types"
import { ClientSearch } from "./client-search"
import { useState } from "react"

// Validation schema
const vehicleSchema = z.object({
  clientId: z.string().min(1, "Debe seleccionar un cliente"),
  brand: z.string().min(1, "La marca es requerida").min(2, "La marca debe tener al menos 2 caracteres"),
  model: z.string().min(1, "El modelo es requerido").min(2, "El modelo debe tener al menos 2 caracteres"),
  year: z.number().min(1900, "Año inválido").max(new Date().getFullYear() + 1, "Año inválido"),
  plates: z.string().min(1, "Las placas son requeridas").min(6, "Las placas deben tener al menos 6 caracteres"),
  vin: z.string().optional(),
  color: z.string().min(1, "El color es requerido"),
  mileage: z.number().optional(),
  engineType: z.string().optional(),
  transmission: z.nativeEnum(TransmissionType).optional(),
  fuelType: z.nativeEnum(FuelType).optional(),
  notes: z.string().optional(),
})

type VehicleFormData = z.infer<typeof vehicleSchema>

interface VehicleFormProps {
  onSubmit: (data: CreateVehicleData) => void
  onCancel?: () => void
  initialData?: Partial<CreateVehicleData>
  isLoading?: boolean
}

export function VehicleForm({ onSubmit, onCancel, initialData, isLoading = false }: VehicleFormProps) {
  const [selectedClient, setSelectedClient] = useState<Client | null>(null)
  
  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors }
  } = useForm<VehicleFormData>({
    resolver: zodResolver(vehicleSchema),
    defaultValues: {
      clientId: "",
      brand: "",
      model: "",
      year: new Date().getFullYear(),
      plates: "",
      vin: "",
      color: "",
      mileage: undefined,
      engineType: "",
      transmission: undefined,
      fuelType: undefined,
      notes: "",
      ...initialData
    }
  })

  const handleFormSubmit = (data: VehicleFormData) => {
    // Clean up empty strings and undefined values
    const cleanData: CreateVehicleData = {
      clientId: data.clientId,
      brand: data.brand.trim(),
      model: data.model.trim(),
      year: data.year,
      plates: data.plates.trim().toUpperCase(),
      color: data.color.trim(),
      ...(data.vin && { vin: data.vin.trim().toUpperCase() }),
      ...(data.mileage && { mileage: data.mileage }),
      ...(data.engineType && { engineType: data.engineType.trim() }),
      ...(data.transmission && { transmission: data.transmission }),
      ...(data.fuelType && { fuelType: data.fuelType }),
      ...(data.notes && { notes: data.notes.trim() })
    }
    
    onSubmit(cleanData)
  }

  const handleClientSelect = (client: Client | null) => {
    setSelectedClient(client)
    setValue("clientId", client?.id || "")
  }

  // Generate year options
  const currentYear = new Date().getFullYear()
  const years = Array.from({ length: currentYear - 1980 + 2 }, (_, i) => currentYear + 1 - i)

  return (
    <Card className="max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Car className="h-5 w-5" />
          Registro de Vehículo
        </CardTitle>
        <CardDescription>
          Completa la información del vehículo
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
          {/* Client Selection */}
          <div className="space-y-2">
            <Label>Cliente *</Label>
            <ClientSearch onClientSelect={handleClientSelect} selectedClient={selectedClient} />
            {errors.clientId && (
              <div className="flex items-center gap-2 text-sm text-red-500">
                <AlertCircle className="h-4 w-4" />
                {errors.clientId.message}
              </div>
            )}
          </div>

          {/* Vehicle Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Información del Vehículo</h3>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="brand">Marca *</Label>
                <Input
                  id="brand"
                  {...register("brand")}
                  className={errors.brand ? "border-red-500" : ""}
                  placeholder="Toyota, Ford, Chevrolet..."
                />
                {errors.brand && (
                  <div className="flex items-center gap-2 text-sm text-red-500">
                    <AlertCircle className="h-4 w-4" />
                    {errors.brand.message}
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="model">Modelo *</Label>
                <Input
                  id="model"
                  {...register("model")}
                  className={errors.model ? "border-red-500" : ""}
                  placeholder="Corolla, F-150, Silverado..."
                />
                {errors.model && (
                  <div className="flex items-center gap-2 text-sm text-red-500">
                    <AlertCircle className="h-4 w-4" />
                    {errors.model.message}
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="year">Año *</Label>
                <Select
                  value={watch("year")?.toString()}
                  onValueChange={(value) => setValue("year", parseInt(value))}
                >
                  <SelectTrigger className={errors.year ? "border-red-500" : ""}>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {years.map((year) => (
                      <SelectItem key={year} value={year.toString()}>
                        {year}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.year && (
                  <div className="flex items-center gap-2 text-sm text-red-500">
                    <AlertCircle className="h-4 w-4" />
                    {errors.year.message}
                  </div>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="color">Color *</Label>
                <Input
                  id="color"
                  {...register("color")}
                  className={errors.color ? "border-red-500" : ""}
                  placeholder="Blanco, Negro, Rojo..."
                />
                {errors.color && (
                  <div className="flex items-center gap-2 text-sm text-red-500">
                    <AlertCircle className="h-4 w-4" />
                    {errors.color.message}
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="plates">Placas *</Label>
                <Input
                  id="plates"
                  {...register("plates")}
                  className={errors.plates ? "border-red-500" : ""}
                  placeholder="ABC-123-D"
                  onChange={(e) => {
                    e.target.value = e.target.value.toUpperCase()
                    setValue("plates", e.target.value)
                  }}
                />
                {errors.plates && (
                  <div className="flex items-center gap-2 text-sm text-red-500">
                    <AlertCircle className="h-4 w-4" />
                    {errors.plates.message}
                  </div>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="vin">VIN (Opcional)</Label>
                <Input
                  id="vin"
                  {...register("vin")}
                  placeholder="Número de identificación vehicular"
                  onChange={(e) => {
                    e.target.value = e.target.value.toUpperCase()
                    setValue("vin", e.target.value)
                  }}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="mileage">Kilometraje (Opcional)</Label>
                <Input
                  id="mileage"
                  type="number"
                  {...register("mileage", { valueAsNumber: true })}
                  placeholder="150000"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="fuelType">Tipo de Combustible</Label>
                <Select
                  value={watch("fuelType")}
                  onValueChange={(value) => setValue("fuelType", value as FuelType)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Seleccionar..." />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={FuelType.GASOLINE}>Gasolina</SelectItem>
                    <SelectItem value={FuelType.DIESEL}>Diésel</SelectItem>
                    <SelectItem value={FuelType.HYBRID}>Híbrido</SelectItem>
                    <SelectItem value={FuelType.ELECTRIC}>Eléctrico</SelectItem>
                    <SelectItem value={FuelType.GAS}>Gas</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="transmission">Transmisión</Label>
                <Select
                  value={watch("transmission")}
                  onValueChange={(value) => setValue("transmission", value as TransmissionType)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Seleccionar..." />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={TransmissionType.MANUAL}>Manual</SelectItem>
                    <SelectItem value={TransmissionType.AUTOMATIC}>Automática</SelectItem>
                    <SelectItem value={TransmissionType.CVT}>CVT</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="engineType">Tipo de Motor (Opcional)</Label>
                <Input
                  id="engineType"
                  {...register("engineType")}
                  placeholder="V6, 4 cilindros, etc."
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="notes">Notas Adicionales (Opcional)</Label>
              <Textarea
                id="notes"
                {...register("notes")}
                placeholder="Información adicional sobre el vehículo..."
                rows={3}
              />
            </div>
          </div>

          {/* Submit Buttons */}
          <div className="flex gap-3 pt-4">
            <Button type="submit" disabled={isLoading} className="flex-1">
              {isLoading ? 'Guardando...' : 'Registrar Vehículo'}
            </Button>
            {onCancel && (
              <Button type="button" variant="outline" onClick={onCancel}>
                Cancelar
              </Button>
            )}
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
