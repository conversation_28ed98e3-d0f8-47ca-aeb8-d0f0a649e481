'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Car, Plus, Search, Calendar, User, Phone, Wrench } from 'lucide-react'
import Link from 'next/link'
import { DashboardHeader } from '@/components/ui/dashboard-header'
import { useQuery } from '@tanstack/react-query'
import { server } from '@/app/api/server'

export default function VehiclesPage() {
  const [search, setSearch] = useState('')
  const [enTaller, setEnTaller] = useState(false)

  const { data: vehiclesData, isLoading } = useQuery({
    queryKey: ['vehicles', { search, enTaller }],
    queryFn: async () => {
      const response = await server.api.vehicles.get({
        query: {
          search: search || undefined,
          enTaller: enTaller ? 'true' : undefined
        }
      })
      return response.data
    }
  })

  const vehicles = vehiclesData?.success ? vehiclesData.data : []

  const getVehicleStatus = (vehicle: any) => {
    if (vehicle.receptions && vehicle.receptions.length > 0) {
      const lastReception = vehicle.receptions[0]
      const statusColors = {
        'RECEIVED': 'bg-blue-100 text-blue-800',
        'IN_DIAGNOSTIC': 'bg-yellow-100 text-yellow-800',
        'WAITING_APPROVAL': 'bg-orange-100 text-orange-800',
        'IN_REPAIR': 'bg-purple-100 text-purple-800',
        'COMPLETED': 'bg-green-100 text-green-800',
        'DELIVERED': 'bg-gray-100 text-gray-800'
      }

      const statusLabels = {
        'RECEIVED': 'Recibido',
        'IN_DIAGNOSTIC': 'En Diagnóstico',
        'WAITING_APPROVAL': 'Esperando Aprobación',
        'IN_REPAIR': 'En Reparación',
        'COMPLETED': 'Terminado',
        'DELIVERED': 'Entregado'
      }

      return (
        <Badge className={statusColors[lastReception.status as keyof typeof statusColors] || 'bg-gray-100 text-gray-800'}>
          {statusLabels[lastReception.status as keyof typeof statusLabels] || lastReception.status}
        </Badge>
      )
    }

    return (
      <Badge variant="outline">
        Fuera del taller
      </Badge>
    )
  }

  const formatFecha = (fecha: string) => {
    return new Date(fecha).toLocaleDateString('es-MX', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    })
  }

  return (
    <div className="p-6">
      <div className="max-w-7xl mx-auto">
        <div className="flex items-center justify-between mb-8">
          <DashboardHeader
            title="Gestión de Vehículos"
            description="Administra todos los vehículos registrados"
            showBackButton={false}
          />
          <Button asChild>
            <Link href="/vehiculos/nuevo">
              <Plus className="w-4 h-4 mr-2" />
              Registrar Vehículo
            </Link>
          </Button>
        </div>

        {/* Filtros */}
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder="Buscar por marca, modelo, placas, VIN o cliente..."
                    value={search}
                    onChange={(e) => setSearch(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <Button
                  variant={!enTaller ? 'default' : 'outline'}
                  onClick={() => setEnTaller(false)}
                  size="sm"
                >
                  Todos
                </Button>
                <Button
                  variant={enTaller ? 'default' : 'outline'}
                  onClick={() => setEnTaller(true)}
                  size="sm"
                >
                  <Wrench className="w-4 h-4 mr-1" />
                  En Taller
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Lista de Vehículos */}
        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <Card key={i} className="animate-pulse">
                <CardContent className="pt-6">
                  <div className="space-y-3">
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                    <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : vehicles.length === 0 ? (
          <Card>
            <CardContent className="pt-6">
              <div className="text-center py-12">
                <Car className="h-16 w-16 mx-auto text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  No hay vehículos registrados
                </h3>
                <p className="text-gray-500 mb-6">
                  {search || enTaller 
                    ? 'No se encontraron vehículos con los filtros aplicados'
                    : 'Comienza registrando el primer vehículo'
                  }
                </p>
                <Button asChild>
                  <Link href="/vehiculos/nuevo">
                    <Plus className="w-4 h-4 mr-2" />
                    Registrar Vehículo
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {vehicles.map((vehicle: any) => (
              <Card key={vehicle.id} className="hover:shadow-lg transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <CardTitle className="text-lg">
                        {vehicle.brand} {vehicle.model}
                      </CardTitle>
                      <CardDescription className="font-medium">
                        {vehicle.year} • {vehicle.plates}
                      </CardDescription>
                    </div>
                    {getVehicleStatus(vehicle)}
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {vehicle.color && (
                      <div className="text-sm text-gray-600">
                        <span className="font-medium">Color:</span> {vehicle.color}
                      </div>
                    )}

                    {vehicle.vin && (
                      <div className="text-sm text-gray-600">
                        <span className="font-medium">VIN:</span> {vehicle.vin}
                      </div>
                    )}

                    <div className="border-t pt-3">
                      <div className="flex items-center gap-2 text-sm text-gray-600 mb-2">
                        <User className="w-4 h-4" />
                        <span className="font-medium">{vehicle.client.name}</span>
                      </div>

                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <Phone className="w-4 h-4" />
                        <span>{vehicle.client.phone}</span>
                      </div>

                      {vehicle.client.clientType === 'FLEET' && (
                        <div className="mt-2">
                          <Badge variant="secondary" className="text-xs">
                            Flotillero
                          </Badge>
                        </div>
                      )}
                    </div>

                    {vehicle.receptions && vehicle.receptions.length > 0 && (
                      <div className="border-t pt-3">
                        <div className="flex items-center gap-2 text-sm text-gray-600">
                          <Calendar className="w-4 h-4" />
                          <span>
                            Última recepción: {formatFecha(vehicle.receptions[0].createdAt)}
                          </span>
                        </div>
                        {vehicle.receptions[0].receptionist && (
                          <div className="text-xs text-gray-500 mt-1">
                            Por: {vehicle.receptions[0].receptionist.name}
                          </div>
                        )}
                      </div>
                    )}

                    <div className="text-xs text-gray-500">
                      {vehicle._count?.receptions || 0} recepción{vehicle._count?.receptions !== 1 ? 'es' : ''} total{vehicle._count?.receptions !== 1 ? 'es' : ''}
                    </div>
                  </div>

                  <div className="mt-4 flex gap-2">
                    <Button asChild size="sm" className="flex-1">
                      <Link href={`/vehiculos/${vehicle.id}`}>
                        Ver Detalles
                      </Link>
                    </Button>
                    <Button asChild variant="outline" size="sm">
                      <Link href={`/vehiculos/${vehicle.id}/editar`}>
                        Editar
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Resumen */}
        {vehicles.length > 0 && (
          <Card className="mt-8">
            <CardHeader>
              <CardTitle className="text-lg">Resumen</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-blue-600">
                    {vehicles.length}
                  </div>
                  <div className="text-sm text-gray-500">Total</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-green-600">
                    {vehicles.filter((v: any) =>
                      v.receptions && v.receptions.length > 0 &&
                      ['RECEIVED', 'IN_DIAGNOSTIC', 'IN_REPAIR'].includes(v.receptions[0].status)
                    ).length}
                  </div>
                  <div className="text-sm text-gray-500">En Taller</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-purple-600">
                    {vehicles.filter((v: any) => v.client.clientType === 'FLEET').length}
                  </div>
                  <div className="text-sm text-gray-500">Flotilleros</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-orange-600">
                    {vehicles.filter((v: any) => v.client.clientType === 'INDIVIDUAL').length}
                  </div>
                  <div className="text-sm text-gray-500">Individuales</div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
