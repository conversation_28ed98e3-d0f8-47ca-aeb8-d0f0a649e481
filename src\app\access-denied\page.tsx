"use client"

import { useSearchParams } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Shield, Mail, ArrowLeft, UserX, Clock } from 'lucide-react'
import Link from 'next/link'
import { authClient } from '@/lib/auth-client'

export default function AccessDeniedPage() {
  const searchParams = useSearchParams()
  const reason = searchParams.get('reason')
  const email = searchParams.get('email')

  const getIcon = () => {
    if (reason?.includes('invitado')) return <UserX className="h-16 w-16 text-destructive" />
    if (reason?.includes('desactivada')) return <Shield className="h-16 w-16 text-orange-500" />
    if (reason?.includes('talleres')) return <Shield className="h-16 w-16 text-blue-500" />
    return <Shield className="h-16 w-16 text-destructive" />
  }

  const getTitle = () => {
    if (reason?.includes('invitado')) return 'Acceso Restringido'
    if (reason?.includes('desactivada')) return 'Cuenta Desactivada'
    if (reason?.includes('talleres')) return 'Sin Talleres Asignados'
    return 'Acceso Denegado'
  }

  const getDescription = () => {
    if (reason?.includes('invitado')) {
      return 'Solo usuarios invitados pueden acceder al sistema'
    }
    if (reason?.includes('desactivada')) {
      return 'Tu cuenta ha sido desactivada temporalmente'
    }
    if (reason?.includes('talleres')) {
      return 'No tienes talleres asignados para trabajar'
    }
    return 'No tienes permisos para acceder a esta aplicación'
  }

  const getInstructions = () => {
    if (reason?.includes('invitado')) {
      return (
        <div className="space-y-4">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="font-semibold text-blue-900 mb-2">¿Cómo obtener acceso?</h4>
            <ol className="list-decimal list-inside space-y-2 text-sm text-blue-800">
              <li>Solicita a un administrador que te envíe una invitación</li>
              <li>Revisa tu email para el enlace de invitación</li>
              <li>Haz clic en el enlace para activar tu cuenta</li>
              <li>Inicia sesión nuevamente</li>
            </ol>
          </div>
          
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <Mail className="h-4 w-4 text-yellow-600" />
              <h4 className="font-semibold text-yellow-900">Contacto para Invitaciones</h4>
            </div>
            <p className="text-sm text-yellow-800">
              Si necesitas acceso urgente, contacta al administrador del sistema con tu email: 
              <span className="font-mono bg-yellow-100 px-1 rounded">{email}</span>
            </p>
          </div>
        </div>
      )
    }

    return (
      <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
        <div className="flex items-center gap-2 mb-2">
          <Clock className="h-4 w-4 text-orange-600" />
          <h4 className="font-semibold text-orange-900">¿Qué hacer?</h4>
        </div>
        <p className="text-sm text-orange-800">
          Contacta al administrador del sistema para resolver este problema.
          Proporciona tu email: <span className="font-mono bg-orange-100 px-1 rounded">{email}</span>
        </p>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="w-full max-w-2xl">
        <Card>
          <CardHeader className="text-center pb-6">
            <div className="flex justify-center mb-4">
              {getIcon()}
            </div>
            <CardTitle className="text-2xl">{getTitle()}</CardTitle>
            <CardDescription className="text-base">
              {getDescription()}
            </CardDescription>
          </CardHeader>
          
          <CardContent className="space-y-6">
            {reason && (
              <div className="bg-gray-50 border rounded-lg p-4">
                <h4 className="font-semibold text-gray-900 mb-2">Motivo específico:</h4>
                <p className="text-sm text-gray-700">{reason}</p>
              </div>
            )}

            {getInstructions()}

            <div className="flex flex-col sm:flex-row gap-3 pt-4">
              <Button 
                onClick={async () => {
                  await authClient.signOut()
                  window.location.href = '/login'
                }}
              variant="outline" className="flex-1">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Cerrar Sesión
              </Button>
              
              <Button asChild className="flex-1">
                <Link href={`mailto:<EMAIL>?subject=Solicitud de Acceso&body=Hola, necesito acceso al sistema de gestión de taller. Mi email es: ${email || ''}`}>
                  <Mail className="w-4 h-4 mr-2" />
                  Solicitar Acceso
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* System Info */}
        <div className="mt-8 text-center">
          <div className="inline-flex items-center gap-2 text-sm text-gray-500">
            <Shield className="h-4 w-4" />
            <span>Sistema de Gestión de Taller - Ruedda</span>
          </div>
          <p className="text-xs text-gray-400 mt-1">
            Acceso controlado por invitación para garantizar la seguridad
          </p>
        </div>
      </div>
    </div>
  )
}
