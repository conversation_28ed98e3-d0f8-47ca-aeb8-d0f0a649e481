"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { StatusBadge } from "@/components/status-badge"
import { Car, Clock, DollarSign, Users, TrendingUp, TrendingDown } from "lucide-react"
import { useQuery } from "@tanstack/react-query"
import { server } from "@/app/api/server"
import { useRole } from "@/hooks/use-role"
import { UserRole } from "@/types"

interface StatCardProps {
  title: string
  value: string | number
  description?: string
  trend?: string
  trendDirection?: 'up' | 'down' | 'neutral'
  icon: React.ComponentType<{ className?: string }>
  loading?: boolean
}

function StatCard({ title, value, description, trend, trendDirection, icon: Icon, loading }: StatCardProps) {
  const getTrendIcon = () => {
    if (!trend || trendDirection === 'neutral') return null
    return trendDirection === 'up' ? 
      <TrendingUp className="h-3 w-3 text-green-500" /> : 
      <TrendingDown className="h-3 w-3 text-red-500" />
  }

  const getTrendColor = () => {
    if (!trend || trendDirection === 'neutral') return 'text-muted-foreground'
    return trendDirection === 'up' ? 'text-green-600' : 'text-red-600'
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-muted-foreground">{title}</CardTitle>
        <Icon className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="space-y-2">
            <div className="h-8 bg-muted animate-pulse rounded"></div>
            <div className="h-4 bg-muted animate-pulse rounded w-3/4"></div>
          </div>
        ) : (
          <>
            <div className="text-2xl font-bold text-foreground">{value}</div>
            {description && (
              <p className="text-xs text-muted-foreground mt-1">{description}</p>
            )}
            {trend && (
              <div className={`flex items-center gap-1 text-xs mt-1 ${getTrendColor()}`}>
                {getTrendIcon()}
                <span>{trend}</span>
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  )
}

export function DashboardStats() {
  const { currentRole } = useRole()

  // Query for dashboard statistics
  const { data: stats, isLoading } = useQuery({
    queryKey: ['dashboard-stats', currentRole],
    queryFn: async () => {
      try {
        // For now, return mock data since we don't have the API endpoint yet
        // In a real implementation, this would call the actual API
        return {
          vehiclesInWorkshop: 12,
          pendingServices: 8,
          monthlyRevenue: 45230,
          activeClients: 156,
          completedToday: 3,
          avgRepairTime: 2.5,
          customerSatisfaction: 4.8,
          partsInventory: 89
        }
      } catch (error) {
        console.error('Error fetching dashboard stats:', error)
        return null
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes
  })

  // Define stats based on user role
  const getStatsForRole = () => {
    if (!stats) return []

    const baseStats = [
      {
        title: "Vehículos en Taller",
        value: stats.vehiclesInWorkshop,
        description: "3 esperando refacciones",
        trend: "+2 desde ayer",
        trendDirection: 'up' as const,
        icon: Car,
      },
      {
        title: "Servicios Pendientes",
        value: stats.pendingServices,
        description: "2 requieren aprobación",
        trend: "-1 desde ayer",
        trendDirection: 'down' as const,
        icon: Clock,
      }
    ]

    switch (currentRole) {
      case UserRole.ADMIN:
      case UserRole.JEFE_TALLER:
        return [
          ...baseStats,
          {
            title: "Facturación del Mes",
            value: `$${stats.monthlyRevenue.toLocaleString()}`,
            description: "Meta: $50,000",
            trend: "+12% vs mes anterior",
            trendDirection: 'up' as const,
            icon: DollarSign,
          },
          {
            title: "Clientes Activos",
            value: stats.activeClients,
            description: "23 flotilleros",
            trend: "+5 nuevos esta semana",
            trendDirection: 'up' as const,
            icon: Users,
          }
        ]

      case UserRole.RECEPCIONISTA:
        return [
          ...baseStats,
          {
            title: "Clientes Activos",
            value: stats.activeClients,
            description: "23 flotilleros",
            trend: "+5 nuevos esta semana",
            trendDirection: 'up' as const,
            icon: Users,
          },
          {
            title: "Completados Hoy",
            value: stats.completedToday,
            description: "Vehículos entregados",
            trend: "Meta: 5 diarios",
            trendDirection: 'neutral' as const,
            icon: Car,
          }
        ]

      case UserRole.TECNICO:
        return [
          {
            title: "Mis Trabajos Pendientes",
            value: 5, // This would come from user-specific data
            description: "2 diagnósticos, 3 reparaciones",
            trend: "+1 asignado hoy",
            trendDirection: 'up' as const,
            icon: Car,
          },
          {
            title: "Completados Hoy",
            value: stats.completedToday,
            description: "Trabajos finalizados",
            trend: "Meta: 3 diarios",
            trendDirection: 'neutral' as const,
            icon: Clock,
          },
          {
            title: "Tiempo Promedio",
            value: `${stats.avgRepairTime}h`,
            description: "Por reparación",
            trend: "-0.3h vs mes anterior",
            trendDirection: 'down' as const,
            icon: TrendingUp,
          },
          {
            title: "Inventario Disponible",
            value: `${stats.partsInventory}%`,
            description: "Refacciones en stock",
            trend: "Nivel óptimo",
            trendDirection: 'neutral' as const,
            icon: Package,
          }
        ]

      case UserRole.COMPRAS:
        return [
          {
            title: "Inventario Disponible",
            value: `${stats.partsInventory}%`,
            description: "Refacciones en stock",
            trend: "+5% esta semana",
            trendDirection: 'up' as const,
            icon: Package,
          },
          {
            title: "Órdenes Pendientes",
            value: 12, // Mock data
            description: "Esperando proveedores",
            trend: "3 urgentes",
            trendDirection: 'neutral' as const,
            icon: Clock,
          },
          {
            title: "Gasto del Mes",
            value: "$18,500",
            description: "En refacciones",
            trend: "-8% vs mes anterior",
            trendDirection: 'down' as const,
            icon: DollarSign,
          },
          {
            title: "Proveedores Activos",
            value: 15,
            description: "Con órdenes abiertas",
            trend: "2 nuevos este mes",
            trendDirection: 'up' as const,
            icon: Users,
          }
        ]

      case UserRole.FACTURACION:
        return [
          {
            title: "Facturación del Mes",
            value: `$${stats.monthlyRevenue.toLocaleString()}`,
            description: "Meta: $50,000",
            trend: "+12% vs mes anterior",
            trendDirection: 'up' as const,
            icon: DollarSign,
          },
          {
            title: "Facturas Pendientes",
            value: 8,
            description: "Por cobrar",
            trend: "Total: $12,300",
            trendDirection: 'neutral' as const,
            icon: Clock,
          },
          {
            title: "Clientes Activos",
            value: stats.activeClients,
            description: "Con servicios facturados",
            trend: "+5 nuevos esta semana",
            trendDirection: 'up' as const,
            icon: Users,
          },
          {
            title: "Satisfacción Cliente",
            value: stats.customerSatisfaction,
            description: "Promedio de calificaciones",
            trend: "+0.2 este mes",
            trendDirection: 'up' as const,
            icon: TrendingUp,
          }
        ]

      default:
        return baseStats
    }
  }

  const roleStats = getStatsForRole()

  return (
    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
      {roleStats.map((stat, index) => (
        <StatCard
          key={index}
          title={stat.title}
          value={stat.value}
          description={stat.description}
          trend={stat.trend}
          trendDirection={stat.trendDirection}
          icon={stat.icon}
          loading={isLoading}
        />
      ))}
    </div>
  )
}

// Import Package icon that was missing
import { Package } from "lucide-react"
