💪️ PRD – Sistema de Gestión de Taller Mecánico
Versión: 1.0
Producto: Plataforma Integral de Operación para Taller Mecánico

🌟 Objetivo del Producto
Diseñar y desarrollar un sistema integral que permita gestionar de forma eficiente todas las operaciones de un taller mecánico, desde la recepción del vehículo, diagnóstico, control de refacciones, ejecución del servicio, evidencia fotográfica, hasta la facturación y generación de reportes, permitiendo también el seguimiento por tipo de cliente (individual o flotillero).

👥 Usuarios Objetivo
- Recepcionista del taller
- Técnicos mecánicos
- Jefe de taller / Supervisor
- Personal de compras
- Área administrativa / facturación
- Clientes corporativos (flotilleros) y clientes particulares
Cada uno de los usuarios tendrá un panel personalizado con las funcionalidades y permisos que corresponden a su rol.

🧭 Flujo General del Sistema
1. 📅 Recepción del Vehículo
Registro de entrada con fecha y hora.
Captura de evidencias:
Fotos del exterior e interior.
Fotos de pertenencias dejadas por el cliente.
Registro de daños visibles.
Selección del tipo de servicio solicitado.
Campo de notas especiales del cliente.
Asociación del vehículo con el cliente correspondiente.
Visualización previa del técnico antes de iniciar el trabajo.

2. 👤 Gestión de Clientes y Tipos de Facturación
Tipos de cliente:
Cliente único:
Cuenta con 1 unidad.
Pago normalmente contra entrega.
Facturación individual por servicio.
Cliente flotillero:
Asociado a varias unidades.
Facturación global periódica (semanal/quincenal/mensual).
Condiciones especiales: crédito, pagos agrupados, reporte consolidado.
Toda la flota visible desde un solo panel de cliente.
Funcionalidades clave:
Registro de datos fiscales, condiciones de pago, razón social.
Filtros por cliente:
Unidades activas, histórico de servicios, pendientes por pagar.
Reportes por cliente:
Por unidad o consolidados para flotas.
Exportación personalizada.

3. 📋 Revisión y Diagnóstico (Actualizado)
Diagnóstico Técnico
El técnico asignado realiza el diagnóstico desde su panel.
Cada falla debe registrarse individualmente, incluyendo:
Descripción técnica.
Refacción o componente afectado.
Evidencia obligatoria:
Foto o video.
Observaciones técnicas (opcional).
Se permite registrar múltiples fallas por vehículo.
Envío a Administración
El diagnóstico completo se envía automáticamente al área administrativa.
El equipo administrativo genera la cotización correspondiente:
Precios preconfigurados por tipo de reparación.
Refacciones, mano de obra, IVA.
Condiciones según tipo de cliente.
Todo queda registrado en el historial del vehículo.

4. 💰 Cotización y Aprobación (Actualizado)
Generación y Enlace
Se genera una cotización con: mano de obra, refacciones e IVA.
Se envía un enlace seguro al cliente por WhatsApp, SMS o correo.
El cliente puede:
Ver todas las fallas detectadas.
Ver evidencias por servicio (foto/video).
Visualizar costo por servicio.
Aprobación Flexible
El cliente puede:
Aprobar todo.
Aprobar servicio por servicio.
Servicios aprobados se liberan para ejecución.
Estimación de Tiempo
El sistema muestra:
Tiempo de reparación estimado.
Si la refacción no está disponible, tiempo de espera estimado.
Se marca el estatus "en espera por refacción" en la orden.
Seguimiento
Registro de:
Servicios aprobados.
Servicios rechazados o pendientes.
Hora y fecha de aprobación.

5. 🧩 Gestión de Refacciones e Inventario (Actualizado)
Asignación por Servicio
Cada servicio aprobado que requiera refacción debe tener una pieza asignada.
Si no está en inventario:
Se marca como pendiente por pedir.
Se notifica al área de compras.
Refacciones Pendientes
Se listan con:
Servicio asociado.
Vehículo y cliente.
Fecha de solicitud.
Proveedor asignado, tiempo estimado de llegada, costo unitario y factura.
Estatus: solicitada / en camino / recibida / en uso.
Control de Inventario
Registro por:
Nombre, modelo, código, proveedor, precio.
Stock actual y stock mínimo.
Las piezas asignadas a servicios se reservan.
Al finalizar el servicio, se descuentan automáticamente.
Carga de Inventario
Se puede subir inventario manual o por archivo CSV.
Adjuntar factura en PDF o imagen.
Al subir nuevas piezas:
Se actualiza el stock.
Se eliminan alertas si aplica.
Panel Central de Refacciones
Muestra:
En stock, reservadas, pendientes de compra.
Tiempos de entrega, proveedor y costos.
Historial de uso por vehículo y técnico.
Reportes mensuales de gasto.

6. 🥷️ Gestión de Mano de Obra, Pipeline y Comisiones (Actualizado)
Registro de Jornada
Los técnicos se loguean para registrar:
Entrada/salida.
Horas efectivas trabajadas.
Asignación de Reparaciones
Manual o sugerida automáticamente.
Basado en:
Horas disponibles.
Carga actual.
Historial de eficiencia.
tipo de reparaciones
ej 
hojalateria – solo hojalatero
mecánica – se hace por mecánico
Pipeline de Técnico
Vista por técnico:
Trabajo actual.
Cola de trabajos siguientes.
Solo se permite iniciar si ya están las refacciones listas.
Evidencia del Trabajo
Cada orden requiere:
Fotos antes y después.
Evidencia opcional de piezas usadas.
Comisiones
Calculadas con base en:
Costo de servicio.
Bono por eficiencia.
Penalización si hay sobretiempo excesivo.
Reportes Automáticos
Diario al jefe de taller con:
Servicios, tiempos, evidencias, comisiones.
Semanal a administración:
Consolidado de pagos y productividad.

7. 🚗 Reparación y Ejecución
El sistema valida que el servicio tenga refacciones listas.
El técnico inicia la orden.
Se registra inicio/fin.
Se sube la evidencia obligatoria.

8. 📆 Vista de Coches en Taller
Lista en tiempo real con:
Placa, modelo, cliente, fecha de entrada.
Estatus: esperando refacción / en reparación / terminado.
Costos estimados y reales.
Filtros por:
Cliente, estatus, tipo de servicio, tiempo en taller.





9. 📤 Facturación y Entrega
Generación de factura:
Cliente único: por unidad.
Cliente flotillero: global por periodo.
Registro de entrega:
Firma digital o física.
Confirmación de pago.

10. 📊 Reportes Personalizados
Filtros por:
Fechas, clientes, técnicos, refacciones, estatus.
Reportes internos:
Con tiempos, refacciones, comisiones.
Reportes para clientes:
Sin datos internos.
Exportables en PDF y Excel.
