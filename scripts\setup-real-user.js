const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function setupRealUser() {
  console.log('🔧 Configurando usuario real...')

  const REAL_USER_ID = 'RpTwiCGAvzyL2665z799sz7FZK3slkpe'

  try {
    // Check if user exists
    const user = await prisma.user.findUnique({
      where: { id: REAL_USER_ID },
      include: {
        workshopAccess: true,
        currentWorkshop: true
      }
    })

    if (!user) {
      console.log('❌ Usuario no encontrado con ID:', REAL_USER_ID)
      return
    }

    console.log('✅ Usuario encontrado:', user.name, user.email)

    // Check if there are any workshops
    const workshopCount = await prisma.workshop.count()
    let defaultWorkshop

    if (workshopCount === 0) {
      console.log('📝 No hay talleres, creando taller por defecto...')
      
      // Create default workshop
      defaultWorkshop = await prisma.workshop.create({
        data: {
          name: '<PERSON><PERSON> Principal',
          location: 'Ubicac<PERSON> Principal',
          phone: '+52 55 0000 0000',
          email: '<EMAIL>',
          isActive: true,
          settings: {
            businessHours: {
              monday: { open: '08:00', close: '18:00' },
              tuesday: { open: '08:00', close: '18:00' },
              wednesday: { open: '08:00', close: '18:00' },
              thursday: { open: '08:00', close: '18:00' },
              friday: { open: '08:00', close: '18:00' },
              saturday: { open: '08:00', close: '14:00' },
              sunday: { closed: true }
            },
            currency: 'MXN',
            timezone: 'America/Mexico_City',
            features: {
              photoEvidence: true,
              diagnostics: true,
              inventory: true,
              billing: true
            }
          }
        }
      })
      
      console.log('✅ Taller por defecto creado:', defaultWorkshop.name)
    } else {
      // Get the first workshop
      defaultWorkshop = await prisma.workshop.findFirst({
        where: { isActive: true }
      })
      console.log('✅ Usando taller existente:', defaultWorkshop.name)
    }

    // Check if user is already assigned to this workshop
    const existingAssignment = await prisma.userWorkshop.findUnique({
      where: {
        userId_workshopId: {
          userId: REAL_USER_ID,
          workshopId: defaultWorkshop.id
        }
      }
    })

    if (!existingAssignment) {
      // Assign user to workshop as ADMIN
      await prisma.userWorkshop.create({
        data: {
          userId: REAL_USER_ID,
          workshopId: defaultWorkshop.id,
          role: 'ADMIN',
          isActive: true,
          assignedAt: new Date()
        }
      })
      console.log('✅ Usuario asignado al taller como ADMIN')
    } else {
      // Update existing assignment to ADMIN
      await prisma.userWorkshop.update({
        where: { id: existingAssignment.id },
        data: {
          role: 'ADMIN',
          isActive: true
        }
      })
      console.log('✅ Asignación existente actualizada a ADMIN')
    }

    // Set as current workshop
    await prisma.user.update({
      where: { id: REAL_USER_ID },
      data: { 
        currentWorkshopId: defaultWorkshop.id,
        role: 'ADMIN'
      }
    })

    console.log('🎉 Usuario configurado exitosamente!')
    console.log(`   • Usuario: ${user.name} (${user.email})`)
    console.log(`   • Rol: ADMIN`)
    console.log(`   • Taller actual: ${defaultWorkshop.name}`)
    console.log(`   • ID del taller: ${defaultWorkshop.id}`)

  } catch (error) {
    console.error('❌ Error configurando usuario:', error)
  } finally {
    await prisma.$disconnect()
  }
}

setupRealUser()
