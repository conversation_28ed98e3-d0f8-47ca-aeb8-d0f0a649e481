'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Car, Plus, Search, Calendar, Clock, User, FileText } from 'lucide-react'
import Link from 'next/link'
import { DashboardHeader } from '@/components/ui/dashboard-header'
import { useQuery } from '@tanstack/react-query'
import { server } from '@/app/api/server'
import { useRole } from '@/hooks/use-role'

export default function ReceptionPage() {
  const [search, setSearch] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const { currentWorkshopId } = useRole();
  console.log('Current workshop ID:', currentWorkshopId)

  const { data: receptions } = useQuery({
    queryKey: ['receptions', { search, status: statusFilter, workshopId: currentWorkshopId }],
    queryFn: async () => {
      const response = await server.api.receptions.get({
        query: {
          status: statusFilter || undefined
        }
      })
      if (response.error) {
        throw new Error(response.error.value.message)
      }
      return response.data.data
    }
  })


  const getStatusBadge = (status: string) => {
    const statusConfig = {
      'RECEIVED': { color: 'bg-blue-100 text-blue-800', label: 'Recibido' },
      'IN_DIAGNOSTIC': { color: 'bg-yellow-100 text-yellow-800', label: 'En Diagnóstico' },
      'WAITING_APPROVAL': { color: 'bg-orange-100 text-orange-800', label: 'Esperando Aprobación' },
      'WAITING_PARTS': { color: 'bg-purple-100 text-purple-800', label: 'Esperando Refacciones' },
      'IN_REPAIR': { color: 'bg-indigo-100 text-indigo-800', label: 'En Reparación' },
      'COMPLETED': { color: 'bg-green-100 text-green-800', label: 'Terminado' },
      'DELIVERED': { color: 'bg-gray-100 text-gray-800', label: 'Entregado' }
    }

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.RECEIVED

    return (
      <Badge className={config.color}>
        {config.label}
      </Badge>
    )
  }

  const formatFecha = (fecha: string) => {
    return new Date(fecha).toLocaleDateString('es-MX', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <div className="p-6">
      <div className="max-w-7xl mx-auto">
        <div className="flex items-center justify-between mb-8">
          <DashboardHeader
            title="Recepción de Vehículos"
            description="Gestiona la recepción y seguimiento de vehículos en el taller"
            showBackButton={false}
          />
          <Button asChild size="lg">
            <Link href="/recepcion/nuevo">
              <Plus className="w-5 h-5 mr-2" />
              Nueva Recepción
            </Link>
          </Button>
        </div>

        {/* Métricas Rápidas */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Vehículos Recibidos Hoy</CardTitle>
              <Car className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">0</div>
              <p className="text-xs text-muted-foreground">
                Nuevas recepciones
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">En Diagnóstico</CardTitle>
              <Search className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">0</div>
              <p className="text-xs text-muted-foreground">
                Pendientes de diagnóstico
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">En Reparación</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">0</div>
              <p className="text-xs text-muted-foreground">
                Trabajos en proceso
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Listos para Entrega</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">0</div>
              <p className="text-xs text-muted-foreground">
                Trabajos terminados
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Filtros */}
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder="Buscar por cliente, vehículo, placas..."
                    value={search}
                    onChange={(e) => setSearch(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="flex gap-2 flex-wrap">
                <Button
                  variant={statusFilter === '' ? 'default' : 'outline'}
                  onClick={() => setStatusFilter('')}
                  size="sm"
                >
                  Todos
                </Button>
                <Button
                  variant={statusFilter === 'RECEIVED' ? 'default' : 'outline'}
                  onClick={() => setStatusFilter('RECEIVED')}
                  size="sm"
                >
                  Recibidos
                </Button>
                <Button
                  variant={statusFilter === 'IN_DIAGNOSTIC' ? 'default' : 'outline'}
                  onClick={() => setStatusFilter('IN_DIAGNOSTIC')}
                  size="sm"
                >
                  En Diagnóstico
                </Button>
                <Button
                  variant={statusFilter === 'IN_REPAIR' ? 'default' : 'outline'}
                  onClick={() => setStatusFilter('IN_REPAIR')}
                  size="sm"
                >
                  En Reparación
                </Button>
                <Button
                  variant={statusFilter === 'COMPLETED' ? 'default' : 'outline'}
                  onClick={() => setStatusFilter('COMPLETED')}
                  size="sm"
                >
                  Terminados
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Lista de Recepciones */}
        {receptions?.length === 0 ? (
          <Card>
            <CardContent className="pt-6">
              <div className="text-center py-12">
                <Car className="h-16 w-16 mx-auto text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  No hay recepciones registradas
                </h3>
                <p className="text-gray-500 mb-6">
                  Comienza registrando la primera recepción de vehículo
                </p>
                <Button asChild size="lg">
                  <Link href="/recepcion/nuevo">
                    <Plus className="w-5 h-5 mr-2" />
                    Nueva Recepción
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-4">
            {receptions?.map((reception: any) => (
              <Card key={reception.id} className="hover:shadow-lg transition-shadow">
                <CardContent className="pt-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="text-lg font-semibold">
                          {reception.vehicle.brand} {reception.vehicle.model} {reception.vehicle.year}
                        </h3>
                        {getStatusBadge(reception.status)}
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                        <div>
                          <div className="flex items-center gap-2 mb-1">
                            <User className="w-4 h-4" />
                            <span className="font-medium">{reception.client.name}</span>
                          </div>
                          <div className="text-xs text-gray-500">
                            {reception.client.phone}
                          </div>
                        </div>

                        <div>
                          <div className="flex items-center gap-2 mb-1">
                            <Car className="w-4 h-4" />
                            <span className="font-medium">Placas: {reception.vehicle.plates}</span>
                          </div>
                          <div className="text-xs text-gray-500">
                            Kilometraje: {reception.mileage.toLocaleString()} km
                          </div>
                        </div>

                        <div>
                          <div className="flex items-center gap-2 mb-1">
                            <Calendar className="w-4 h-4" />
                            <span className="font-medium">Recibido</span>
                          </div>
                          <div className="text-xs text-gray-500">
                            {formatFecha(reception.entryDate)}
                          </div>
                        </div>
                      </div>

                      <div className="mt-3 text-sm">
                        <span className="font-medium">Servicio:</span> {reception.serviceType.replace('_', ' ')}
                        {reception.otherService && ` - ${reception.otherService}`}
                      </div>

                      {reception.observations && (
                        <div className="mt-2 text-sm text-gray-600">
                          <span className="font-medium">Observaciones:</span> {reception.observations}
                        </div>
                      )}
                    </div>

                    <div className="flex gap-2 ml-4">
                      <Button asChild size="sm">
                        <Link href={`/recepcion/${reception.id}`}>
                          Ver Detalles
                        </Link>
                      </Button>
                      <Button asChild variant="outline" size="sm">
                        <Link href={`/recepcion/${reception.id}/editar`}>
                          Editar
                        </Link>
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
