import { PrismaClient } from '@prisma/client'
import { randomBytes } from 'crypto'

const db = new PrismaClient()

async function createAdminInvitation() {
  try {
    // Verificar si ya existe un admin
    const existingAdmin = await db.user.findFirst({
      where: { role: 'ADMIN' }
    })

    if (existingAdmin) {
      console.log('Ya existe un usuario administrador:', existingAdmin.email)
      return
    }

    // Crear invitación para admin
    const adminEmail = '<EMAIL>' // Cambiar por el email deseado
    const token = randomBytes(32).toString('hex')
    const expiresAt = new Date()
    expiresAt.setDate(expiresAt.getDate() + 30) // 30 días para registrarse

    // Crear un usuario temporal para enviar la invitación
    let tempUser = await db.user.findFirst({
      where: { email: '<EMAIL>' }
    })

    if (!tempUser) {
      tempUser = await db.user.create({
        data: {
          id: 'system-user',
          name: '<PERSON><PERSON><PERSON>',
          email: '<EMAIL>',
          emailVerified: true,
          role: 'ADMIN',
          createdAt: new Date(),
          updatedAt: new Date()
        }
      })
    }

    const invitation = await db.invitation.create({
      data: {
        email: adminEmail,
        role: 'ADMIN',
        token,
        expiresAt,
        sentById: tempUser.id,
        status: 'PENDING'
      }
    })

    console.log('✅ Invitación de administrador creada exitosamente!')
    console.log('📧 Email:', adminEmail)
    console.log('🔗 Token:', token)
    console.log('📅 Expira:', expiresAt.toLocaleDateString())
    console.log('')
    console.log('🌐 URL de registro:')
    console.log(`http://localhost:3000/register?token=${token}`)
    console.log('')
    console.log('⚠️  Guarda esta información de forma segura!')

  } catch (error) {
    console.error('Error al crear la invitación:', error)
  } finally {
    await db.$disconnect()
  }
}

createAdminInvitation()
