"use client"

import { <PERSON>, Card<PERSON>ontent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { StatusBadge, getActivityStatusBadge } from "@/components/status-badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Car, User, CheckCircle, Clock, AlertCircle } from "lucide-react"
import { useQuery } from "@tanstack/react-query"
import { server } from "@/app/api/server"
import { useRole } from "@/hooks/use-role"
import { formatDistanceToNow } from "date-fns"
import { es } from "date-fns/locale"

interface ActivityItem {
  id: string
  type: 'reception' | 'completion' | 'client_created' | 'vehicle_added' | 'diagnostic' | 'approval_needed'
  title: string
  description: string
  timestamp: Date
  user?: {
    name: string
    image?: string
  }
  status?: 'success' | 'warning' | 'info' | 'error'
  relatedId?: string
}

function ActivityIcon({ type, status }: { type: ActivityItem['type'], status?: ActivityItem['status'] }) {
  const getIconColor = () => {
    switch (status) {
      case 'success': return 'text-green-500'
      case 'warning': return 'text-yellow-500'
      case 'error': return 'text-red-500'
      default: return 'text-blue-500'
    }
  }

  const iconClass = `h-4 w-4 ${getIconColor()}`

  switch (type) {
    case 'reception':
      return <Car className={iconClass} />
    case 'completion':
      return <CheckCircle className={iconClass} />
    case 'client_created':
    case 'vehicle_added':
      return <User className={iconClass} />
    case 'diagnostic':
      return <AlertCircle className={iconClass} />
    case 'approval_needed':
      return <Clock className={iconClass} />
    default:
      return <Clock className={iconClass} />
  }
}

function ActivityBadge({ type, status }: { type: ActivityItem['type'], status?: ActivityItem['status'] }) {
  const getTypeLabel = () => {
    switch (type) {
      case 'reception': return 'Recepción'
      case 'completion': return 'Completado'
      case 'client_created': return 'Cliente'
      case 'vehicle_added': return 'Vehículo'
      case 'diagnostic': return 'Diagnóstico'
      case 'approval_needed': return 'Aprobación'
      default: return 'Actividad'
    }
  }

  // Use the new StatusBadge with proper colors
  const statusType = status || 'info'

  return (
    <StatusBadge
      status={statusType}
      customLabel={getTypeLabel()}
      size="sm"
    />
  )
}

export function RecentActivity() {
  const { currentRole } = useRole()

  // Query for recent activity
  const { data: activities, isLoading } = useQuery({
    queryKey: ['recent-activity', currentRole],
    queryFn: async () => {
      try {
        // For now, return mock data since we don't have the API endpoint yet
        // In a real implementation, this would call the actual API
        const mockActivities: ActivityItem[] = [
          {
            id: '1',
            type: 'reception',
            title: 'Nuevo vehículo recibido',
            description: 'Toyota Corolla 2020 - Servicio preventivo mayor',
            timestamp: new Date(Date.now() - 15 * 60 * 1000), // 15 minutes ago
            user: { name: 'María González' },
            status: 'info',
            relatedId: 'vehicle-123'
          },
          {
            id: '2',
            type: 'completion',
            title: 'Servicio completado',
            description: 'Ford F-150 2019 - Cambio de aceite y filtros',
            timestamp: new Date(Date.now() - 45 * 60 * 1000), // 45 minutes ago
            user: { name: 'Carlos Méndez' },
            status: 'success',
            relatedId: 'vehicle-456'
          },
          {
            id: '3',
            type: 'approval_needed',
            title: 'Aprobación requerida',
            description: 'Chevrolet Silverado 2021 - Reparación de transmisión ($8,500)',
            timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
            user: { name: 'Ana Rodríguez' },
            status: 'warning',
            relatedId: 'vehicle-789'
          },
          {
            id: '4',
            type: 'client_created',
            title: 'Nuevo cliente registrado',
            description: 'Transportes del Norte S.A. - Cliente flotillero',
            timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000), // 3 hours ago
            user: { name: 'Luis Herrera' },
            status: 'success',
            relatedId: 'client-101'
          },
          {
            id: '5',
            type: 'diagnostic',
            title: 'Diagnóstico completado',
            description: 'Nissan Sentra 2018 - Problema en sistema eléctrico',
            timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000), // 4 hours ago
            user: { name: 'Roberto Silva' },
            status: 'info',
            relatedId: 'vehicle-321'
          },
          {
            id: '6',
            type: 'vehicle_added',
            title: 'Vehículo agregado',
            description: 'Honda Civic 2022 - Registrado para cliente existente',
            timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000), // 6 hours ago
            user: { name: 'Patricia López' },
            status: 'info',
            relatedId: 'vehicle-654'
          }
        ]

        return mockActivities
      } catch (error) {
        console.error('Error fetching recent activity:', error)
        return []
      }
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchInterval: 2 * 60 * 1000, // Refetch every 2 minutes
  })

  const getUserInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Actividad Reciente</CardTitle>
          <CardDescription>Últimas acciones en el sistema</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="flex items-center space-x-4">
                <div className="w-8 h-8 bg-muted animate-pulse rounded-full"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-muted animate-pulse rounded w-3/4"></div>
                  <div className="h-3 bg-muted animate-pulse rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Actividad Reciente</CardTitle>
        <CardDescription>Últimas acciones en el sistema</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {activities && activities.length > 0 ? (
            activities.slice(0, 6).map((activity) => (
              <div key={activity.id} className="flex items-start space-x-4">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 rounded-full bg-muted flex items-center justify-center">
                    <ActivityIcon type={activity.type} status={activity.status} />
                  </div>
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <p className="text-sm font-medium text-foreground truncate">
                      {activity.title}
                    </p>
                    <ActivityBadge type={activity.type} status={activity.status} />
                  </div>
                  <p className="text-sm text-muted-foreground mt-1">
                    {activity.description}
                  </p>
                  <div className="flex items-center mt-2 text-xs text-muted-foreground">
                    {activity.user && (
                      <>
                        <Avatar className="w-4 h-4 mr-2">
                          <AvatarImage src={activity.user.image} />
                          <AvatarFallback className="text-xs">
                            {getUserInitials(activity.user.name)}
                          </AvatarFallback>
                        </Avatar>
                        <span className="mr-2">{activity.user.name}</span>
                        <span className="mr-2">•</span>
                      </>
                    )}
                    <span>
                      {formatDistanceToNow(activity.timestamp, { 
                        addSuffix: true, 
                        locale: es 
                      })}
                    </span>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-6 text-muted-foreground">
              <Clock className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p>No hay actividad reciente</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
