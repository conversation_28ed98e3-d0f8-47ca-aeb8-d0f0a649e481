"use client"

import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { useParams } from 'next/navigation'
import { useIsAdmin } from '@/hooks/use-role'
import { server } from '@/app/api/server'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { DashboardHeader } from '@/components/ui/dashboard-header'
import { 
  Users, 
  Search, 
  UserPlus, 
  Mail,
  Shield,
  Wrench,
  UserCheck,
  Calendar,
  Loader2,
  AlertCircle
} from 'lucide-react'
import Link from 'next/link'

interface WorkshopUser {
  id: string
  name: string
  email: string
  role: string
  isActive: boolean
  assignedAt: string
  user: {
    image?: string
    emailVerified: boolean
    createdAt: string
  }
}

interface Workshop {
  id: string
  name: string
  location: string
}

export default function WorkshopUsersPage() {
  const params = useParams()
  const workshopId = params.workshopId as string
  const isAdmin = useIsAdmin()
  const [search, setSearch] = useState('')

  // Fetch workshop details
  const { data: workshop, isLoading: workshopLoading } = useQuery({
    queryKey: ['workshop', workshopId],
    queryFn: async () => {
      const response = await server.api.workshops({ id: workshopId }).get()
      if (!response.data?.success) {
        throw new Error(response.data?.error || 'Failed to fetch workshop')
      }
      return response.data.data as Workshop
    }
  })

  // Fetch workshop users
  const { data: workshopUsers, isLoading: usersLoading, refetch } = useQuery({
    queryKey: ['workshop-users', workshopId],
    queryFn: async () => {
      const response = await server.api.workshops({ id: workshopId }).users.get()
      if (!response.data?.success) {
        throw new Error(response.data?.error || 'Failed to fetch workshop users')
      }
      return response.data.data as WorkshopUser[]
    }
  })

  const users = workshopUsers || []

  const filteredUsers = users.filter(user =>
    user.name.toLowerCase().includes(search.toLowerCase()) ||
    user.email.toLowerCase().includes(search.toLowerCase()) ||
    user.role.toLowerCase().includes(search.toLowerCase())
  )

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'ADMIN':
        return <Shield className="h-4 w-4" />
      case 'RECEPCIONISTA':
        return <UserCheck className="h-4 w-4" />
      case 'TECNICO':
        return <Wrench className="h-4 w-4" />
      default:
        return <Users className="h-4 w-4" />
    }
  }

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'ADMIN':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'RECEPCIONISTA':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'TECNICO':
        return 'bg-green-100 text-green-800 border-green-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  if (!isAdmin) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <Shield className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold">Acceso Restringido</h3>
          <p className="text-muted-foreground">
            Solo los administradores pueden gestionar usuarios de talleres.
          </p>
        </div>
      </div>
    )
  }

  if (workshopLoading || usersLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto mb-4" />
          <p className="text-muted-foreground">Cargando usuarios del taller...</p>
        </div>
      </div>
    )
  }

  if (!workshop) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-destructive mx-auto mb-4" />
          <h3 className="text-lg font-semibold">Taller no encontrado</h3>
          <p className="text-muted-foreground">
            El taller solicitado no existe o no tienes permisos para verlo.
          </p>
          <Button asChild className="mt-4">
            <Link href="/admin/talleres">Volver a Talleres</Link>
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6">
      <div className="max-w-7xl mx-auto">
        <div className="flex items-center justify-between mb-8">
          <DashboardHeader
            title={`Usuarios - ${workshop.name}`}
            description={`Gestiona los usuarios asignados al taller ${workshop.name}`}
            showBackButton={true}
            backUrl={`/admin/talleres`}
          />
          <Button asChild size="lg">
            <Link href={`/admin/talleres/${workshopId}/usuarios/invitar`}>
              <UserPlus className="w-5 h-5 mr-2" />
              Invitar Usuario
            </Link>
          </Button>
        </div>

        {/* Search and Filters */}
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="flex items-center gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="Buscar usuarios por nombre, email o rol..."
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Users Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredUsers.map((workshopUser) => (
            <Card key={workshopUser.id} className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-4">
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                      {getRoleIcon(workshopUser.role)}
                    </div>
                    <div>
                      <CardTitle className="text-lg">{workshopUser.name}</CardTitle>
                      <CardDescription className="flex items-center gap-1">
                        <Mail className="h-3 w-3" />
                        {workshopUser.email}
                      </CardDescription>
                    </div>
                  </div>
                  <Badge className={getRoleColor(workshopUser.role)}>
                    {workshopUser.role}
                  </Badge>
                </div>
              </CardHeader>
              
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Estado:</span>
                  <Badge variant={workshopUser.isActive ? "default" : "secondary"}>
                    {workshopUser.isActive ? "Activo" : "Inactivo"}
                  </Badge>
                </div>
                
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Email verificado:</span>
                  <Badge variant={workshopUser.user.emailVerified ? "default" : "destructive"}>
                    {workshopUser.user.emailVerified ? "Sí" : "No"}
                  </Badge>
                </div>
                
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Calendar className="h-3 w-3" />
                  <span>Asignado: {new Date(workshopUser.assignedAt).toLocaleDateString()}</span>
                </div>

                <div className="flex gap-2 pt-2">
                  <Button variant="outline" size="sm" className="flex-1">
                    Editar
                  </Button>
                  <Button 
                    variant={workshopUser.isActive ? "destructive" : "default"} 
                    size="sm" 
                    className="flex-1"
                  >
                    {workshopUser.isActive ? "Desactivar" : "Activar"}
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredUsers.length === 0 && (
          <Card>
            <CardContent className="pt-6">
              <div className="text-center py-8">
                <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">
                  {search ? 'No se encontraron usuarios' : 'No hay usuarios asignados'}
                </h3>
                <p className="text-muted-foreground mb-4">
                  {search 
                    ? 'Intenta con otros términos de búsqueda'
                    : 'Este taller aún no tiene usuarios asignados'
                  }
                </p>
                {!search && (
                  <Button asChild>
                    <Link href={`/admin/talleres/${workshopId}/usuarios/invitar`}>
                      <UserPlus className="w-4 h-4 mr-2" />
                      Invitar Primer Usuario
                    </Link>
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Summary Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-8">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center gap-2">
                <Users className="h-5 w-5 text-primary" />
                <div>
                  <p className="text-2xl font-bold">{users.length}</p>
                  <p className="text-sm text-muted-foreground">Total Usuarios</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center gap-2">
                <Shield className="h-5 w-5 text-red-500" />
                <div>
                  <p className="text-2xl font-bold">
                    {users.filter(u => u.role === 'ADMIN').length}
                  </p>
                  <p className="text-sm text-muted-foreground">Administradores</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center gap-2">
                <UserCheck className="h-5 w-5 text-blue-500" />
                <div>
                  <p className="text-2xl font-bold">
                    {users.filter(u => u.role === 'RECEPCIONISTA').length}
                  </p>
                  <p className="text-sm text-muted-foreground">Recepcionistas</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center gap-2">
                <Wrench className="h-5 w-5 text-green-500" />
                <div>
                  <p className="text-2xl font-bold">
                    {users.filter(u => u.role === 'TECNICO').length}
                  </p>
                  <p className="text-sm text-muted-foreground">Técnicos</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
