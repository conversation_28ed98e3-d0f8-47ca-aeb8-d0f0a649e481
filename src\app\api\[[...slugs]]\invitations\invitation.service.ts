import db from "@/config/db";
import { CreateInvitationInput } from "./invitation.types";
import { randomBytes } from "crypto";

export class InvitationService {
  
  static async createInvitation(data: CreateInvitationInput, sentById: string) {
    // Verificar si ya existe una invitación pendiente para este email
    const existingInvitation = await db.invitation.findFirst({
      where: {
        email: data.email,
        status: "PENDING",
        expiresAt: {
          gt: new Date()
        }
      }
    });

    if (existingInvitation) {
      throw new Error("Ya existe una invitación pendiente para este email");
    }

    // Verificar si ya existe un usuario con este email
    const existingUser = await db.user.findUnique({
      where: { email: data.email }
    });

    if (existingUser) {
      throw new Error("Ya existe un usuario registrado con este email");
    }

    // Generar token único
    const token = randomBytes(32).toString('hex');
    
    // Crear invitación que expira en 7 días
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7);

    console.log('Creating invitation with workshopIds:', data.workshopIds);

    const invitation = await db.invitation.create({
      data: {
        email: data.email,
        role: data.role,
        token,
        expiresAt,
        sentById,
        status: "PENDING",
        workshopIds: data.workshopIds
      }
    });

    console.log('Created invitation:', invitation);

    return invitation;
  }

  static async listInvitations(options?: {
    page?: number
    limit?: number
    status?: string
    role?: string
    search?: string
  }) {
    const page = options?.page || 1
    const limit = options?.limit || 10
    const skip = (page - 1) * limit

    // Build where clause for filters
    const where: any = {}

    if (options?.status) {
      where.status = options.status
    }

    if (options?.role) {
      where.role = options.role
    }

    if (options?.search) {
      where.email = {
        contains: options.search,
        mode: 'insensitive'
      }
    }

    // Get total count for pagination
    const totalCount = await db.invitation.count({ where })

    // Get paginated results
    const invitations = await db.invitation.findMany({
      where,
      include: {
        sentBy: {
          select: {
            name: true,
            email: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      skip,
      take: limit
    })

    const totalPages = Math.ceil(totalCount / limit)

    return {
      invitations,
      pagination: {
        currentPage: page,
        totalPages,
        totalCount,
        limit,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1
      }
    }
  }

  static async getInvitationByToken(token: string) {
    const invitation = await db.invitation.findUnique({
      where: { token },
      include: {
        sentBy: {
          select: {
            name: true,
            email: true
          }
        }
      }
    });

    if (!invitation) {
      throw new Error("Invitación no encontrada");
    }

    if (invitation.status !== "PENDING") {
      throw new Error("Esta invitación ya ha sido utilizada");
    }

    if (invitation.expiresAt < new Date()) {
      // Marcar como expirada
      await db.invitation.update({
        where: { id: invitation.id },
        data: { status: "EXPIRED" }
      });
      throw new Error("Esta invitación ha expirado");
    }

    return invitation;
  }

  static async cancelInvitation(id: string) {
    const invitation = await db.invitation.findUnique({
      where: { id }
    });

    if (!invitation) {
      throw new Error("Invitación no encontrada");
    }

    if (invitation.status !== "PENDING") {
      throw new Error("Solo se pueden cancelar invitaciones pendientes");
    }

    return await db.invitation.update({
      where: { id },
      data: { status: "CANCELLED" }
    });
  }

  static async acceptInvitation(token: string, userId: string) {
    const invitation = await this.getInvitationByToken(token);

    // Marcar invitación como aceptada
    await db.invitation.update({
      where: { id: invitation.id },
      data: { status: "ACCEPTED" }
    });

    // Actualizar rol del usuario
    await db.user.update({
      where: { id: userId },
      data: { role: invitation.role }
    });

    return invitation;
  }

  static async resendInvitation(id: string) {
    const invitation = await db.invitation.findUnique({
      where: { id }
    });

    if (!invitation) {
      throw new Error("Invitación no encontrada");
    }

    if (invitation.status !== "PENDING") {
      throw new Error("Solo se pueden reenviar invitaciones pendientes");
    }

    if (invitation.expiresAt < new Date()) {
      throw new Error("La invitación ha expirado");
    }

    // TODO: Aquí se implementaría el reenvío del correo
    // Por ahora solo retornamos la invitación
    console.log(`Reenviando invitación a ${invitation.email}`);

    return invitation;
  }
}
