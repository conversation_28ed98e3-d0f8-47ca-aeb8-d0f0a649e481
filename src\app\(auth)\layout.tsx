import { getServerSession } from '@/lib/getSession'
import { redirect } from 'next/navigation'

export default async function AuthLayout({
  children,
}: {
  children: React.ReactNode
}) {
  // Si el usuario ya está autenticado, redirigir al dashboard
  const session = await getServerSession({ shouldRedirect: false })
  
  if (session) {
    redirect('/dashboard')
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {children}
    </div>
  )
}
