'use client'

import Link from "next/link"
import { WorkshopSwitcher } from "./workshop-switcher"
import { cn } from "@/lib/utils"
import { getNavigationForRole } from "@/lib/navigation"
import { UserRole } from "@/types"

interface SidebarProps {
  navigation: any[]
}

export function Sidebar({
  userRole
}: {
  userRole: UserRole
}) {
    console.log('Sidebar userRole:', userRole)
    const navigation = getNavigationForRole(userRole)
  return (
    <div className="flex grow flex-col gap-y-5 overflow-y-auto overflow-x-hidden bg-card border-r border-border px-6 pb-4 w-full">
      <div className="flex h-16 shrink-0 items-center">
        <Link href="/dashboard" className="flex items-center gap-2">
          <div className="h-8 w-8 rounded-lg bg-primary flex items-center justify-center">
            <span className="text-primary-foreground font-bold text-sm">R</span>
          </div>
          <span className="text-lg font-bold text-foreground">Ruedda</span>
        </Link>
      </div>

      <div className="border-b border-border pb-4">
        <WorkshopSwitcher />
      </div>

      <nav className="flex flex-1 flex-col">
        <ul role="list" className="flex flex-1 flex-col gap-y-7">
          <li>
            <ul role="list" className="-mx-2 space-y-1">
              {navigation.map((item) => (
                <li key={item.name}>
                  <Link
                    href={item.href}
                    className={cn(
                      item.current
                        ? "bg-primary/10 text-primary border-r-2 border-primary"
                        : "text-muted-foreground hover:bg-muted hover:text-foreground",
                      "group flex gap-x-3 rounded-l-md p-2 text-sm font-medium leading-6 transition-colors",
                    )}
                  >
                    {item.icon && <item.icon className="h-5 w-5 shrink-0" />}
                    {item.name}
                  </Link>
                </li>
              ))}
            </ul>
          </li>
        </ul>
      </nav>
    </div>
  )
}