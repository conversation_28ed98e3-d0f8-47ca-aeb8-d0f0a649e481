'use client'

import { useState } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Car, 
  ArrowLeft, 
  Edit, 
  User, 
  Phone, 
  Wrench,
  Eye,
  Plus
} from 'lucide-react'
import Link from 'next/link'
import { useQuery } from '@tanstack/react-query'
import { server } from '@/app/api/server'

export default function VehicleDetailPage() {
  const params = useParams()
  const router = useRouter()
  const vehicleId = params.id as string

  const { data: vehicle, isLoading } = useQuery({
    queryKey: ['vehicle', vehicleId],
    queryFn: async () => {
      const response = await server.api.vehicles({ id: vehicleId }).get()
      if (response.error) {
        throw new Error(response.error.value.message)
      }
      return response.data.data
    }
  })

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-6xl mx-auto">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-gray-200 rounded w-1/3"></div>
            <div className="h-64 bg-gray-200 rounded"></div>
            <div className="h-48 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    )
  }

  if (!vehicle) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-6xl mx-auto">
          <div className="text-center py-12">
            <Car className="h-16 w-16 mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Vehículo no encontrado
            </h3>
            <p className="text-gray-500 mb-6">
              El vehículo que buscas no existe o ha sido eliminado
            </p>
            <Button asChild>
              <Link href="/vehiculos">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Volver a Vehículos
              </Link>
            </Button>
          </div>
        </div>
      </div>
    )
  }

  const getVehicleStatus = () => {
    if (vehicle.receptions && vehicle.receptions.length > 0) {
      const lastReception = vehicle.receptions[0]
      const statusColors = {
        'RECEIVED': 'bg-blue-100 text-blue-800',
        'IN_DIAGNOSTIC': 'bg-yellow-100 text-yellow-800',
        'WAITING_APPROVAL': 'bg-orange-100 text-orange-800',
        'IN_REPAIR': 'bg-purple-100 text-purple-800',
        'COMPLETED': 'bg-green-100 text-green-800',
        'DELIVERED': 'bg-gray-100 text-gray-800'
      }

      const statusLabels = {
        'RECEIVED': 'Recibido',
        'IN_DIAGNOSTIC': 'En Diagnóstico',
        'WAITING_APPROVAL': 'Esperando Aprobación',
        'IN_REPAIR': 'En Reparación',
        'COMPLETED': 'Terminado',
        'DELIVERED': 'Entregado'
      }

      return (
        <Badge className={statusColors[lastReception.status as keyof typeof statusColors] || 'bg-gray-100 text-gray-800'}>
          {statusLabels[lastReception.status as keyof typeof statusLabels] || lastReception.status}
        </Badge>
      )
    }

    return (
      <Badge variant="outline">
        Fuera del taller
      </Badge>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-4">
            <Button variant="outline" size="sm" asChild>
              <Link href="/vehiculos">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Volver
              </Link>
            </Button>
          </div>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Car className="h-8 w-8" />
              <div>
                <h1 className="text-3xl font-bold text-gray-900">
                  {vehicle.brand} {vehicle.model} {vehicle.year}
                </h1>
                <div className="flex items-center gap-3 mt-2">
                  <span className="text-gray-600">Placas: {vehicle.plates}</span>
                  {getVehicleStatus()}
                </div>
              </div>
            </div>
            
            <div className="flex gap-2">
              <Button asChild>
                <Link href={`/vehiculos/${vehicle.id}/editar`}>
                  <Edit className="w-4 h-4 mr-2" />
                  Editar
                </Link>
              </Button>
              <Button asChild variant="outline">
                <Link href={`/recepcion/nuevo?vehicleId=${vehicle.id}`}>
                  <Plus className="w-4 h-4 mr-2" />
                  Nueva Recepción
                </Link>
              </Button>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Información Principal */}
          <div className="lg:col-span-2 space-y-6">
            {/* Datos del Vehículo */}
            <Card>
              <CardHeader>
                <CardTitle>Información del Vehículo</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <div className="text-sm text-gray-500">Marca</div>
                    <div className="font-medium">{vehicle.brand}</div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500">Modelo</div>
                    <div className="font-medium">{vehicle.model}</div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500">Año</div>
                    <div className="font-medium">{vehicle.year}</div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500">Placas</div>
                    <div className="font-medium">{vehicle.plates}</div>
                  </div>
                  {vehicle.color && (
                    <div>
                      <div className="text-sm text-gray-500">Color</div>
                      <div className="font-medium">{vehicle.color}</div>
                    </div>
                  )}
                  {vehicle.vin && (
                    <div>
                      <div className="text-sm text-gray-500">VIN</div>
                      <div className="font-medium font-mono text-sm">{vehicle.vin}</div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Información del Cliente */}
            <Card>
              <CardHeader>
                <CardTitle>Propietario</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <User className="w-8 h-8 text-gray-400" />
                    <div>
                      <div className="font-medium text-lg">{vehicle.client.name}</div>
                      <div className="flex items-center gap-2 text-gray-500">
                        <Phone className="w-4 h-4" />
                        <span>{vehicle.client.phone}</span>
                      </div>
                      {vehicle.client.clientType === 'FLEET' && (
                        <Badge variant="secondary" className="mt-1">
                          Cliente Flotillero
                        </Badge>
                      )}
                    </div>
                  </div>
                  <Button asChild variant="outline">
                    <Link href={`/clientes/${vehicle.client.id}`}>
                      <Eye className="w-4 h-4 mr-2" />
                      Ver Cliente
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Historial de Recepciones */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>Historial de Recepciones</CardTitle>
                  <Button asChild size="sm">
                    <Link href={`/recepcion/nuevo?vehicleId=${vehicle.id}`}>
                      <Plus className="w-4 h-4 mr-2" />
                      Nueva Recepción
                    </Link>
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                {vehicle.receptions && vehicle.receptions.length > 0 ? (
                  <div className="space-y-4">
                    {vehicle.receptions.map((recepcion: any) => (
                      <div key={recepcion.id} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center gap-2">
                            <Badge className={
                              recepcion.estatus === 'ENTREGADO' ? 'bg-gray-100 text-gray-800' :
                              recepcion.estatus === 'TERMINADO' ? 'bg-green-100 text-green-800' :
                              recepcion.estatus === 'EN_REPARACION' ? 'bg-purple-100 text-purple-800' :
                              'bg-blue-100 text-blue-800'
                            }>
                              {recepcion.estatus.replace('_', ' ')}
                            </Badge>
                            <span className="text-sm text-gray-500">
                              {new Date(recepcion.createdAt).toLocaleDateString('es-MX')}
                            </span>
                          </div>
                          <Button asChild size="sm" variant="outline">
                            <Link href={`/recepcion/${recepcion.id}`}>
                              Ver Detalle
                            </Link>
                          </Button>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                          <div>
                            <span className="text-gray-500">Servicio:</span>
                            <span className="ml-2">{recepcion.servicioTipo.replace('_', ' ')}</span>
                          </div>
                          <div>
                            <span className="text-gray-500">Kilometraje:</span>
                            <span className="ml-2">{recepcion.kilometraje?.toLocaleString()} km</span>
                          </div>
                          {recepcion.observaciones && (
                            <div className="md:col-span-2">
                              <span className="text-gray-500">Observaciones:</span>
                              <span className="ml-2">{recepcion.observaciones}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <Wrench className="h-12 w-12 mx-auto mb-3 text-gray-300" />
                    <p>No hay receptions registradas</p>
                    <Button asChild size="sm" className="mt-3">
                      <Link href={`/recepcion/nuevo?vehicleId=${vehicle.id}`}>
                        Primera Recepción
                      </Link>
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Estadísticas */}
            <Card>
              <CardHeader>
                <CardTitle>Estadísticas</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">Recepciones</span>
                    <span className="font-semibold">{vehicle.receptions?.length || 0}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">Registrado</span>
                    <span className="font-semibold">
                      {new Date(vehicle.createdAt).toLocaleDateString('es-MX')}
                    </span>
                  </div>
                  {vehicle.receptions && vehicle.receptions.length > 0 && (
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-500">Última visita</span>
                      <span className="font-semibold">
                        {new Date(vehicle.receptions[0].createdAt).toLocaleDateString('es-MX')}
                      </span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Estado Actual */}
            <Card>
              <CardHeader>
                <CardTitle>Estado Actual</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center">
                  {getVehicleStatus()}
                  <p className="text-sm text-gray-500 mt-2">
                    {vehicle.receptions && vehicle.receptions.length > 0
                      ? 'En el taller'
                      : 'Fuera del taller'
                    }
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Acciones Rápidas */}
            <Card>
              <CardHeader>
                <CardTitle>Acciones Rápidas</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Button asChild className="w-full" variant="outline">
                    <Link href={`/recepcion/nuevo?vehicleId=${vehicle.id}`}>
                      <Plus className="w-4 h-4 mr-2" />
                      Nueva Recepción
                    </Link>
                  </Button>
                  <Button asChild className="w-full" variant="outline">
                    <Link href={`/vehiculos/${vehicle.id}/editar`}>
                      <Edit className="w-4 h-4 mr-2" />
                      Editar Vehículo
                    </Link>
                  </Button>
                  <Button asChild className="w-full" variant="outline">
                    <Link href={`/clientes/${vehicle.client.id}`}>
                      <User className="w-4 h-4 mr-2" />
                      Ver Cliente
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
