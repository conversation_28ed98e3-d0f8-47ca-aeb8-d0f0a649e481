'use client'

import { useRouter } from 'next/navigation'
import { DashboardHeader } from '@/components/ui/dashboard-header'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { server } from '@/app/api/server'
import toast from 'react-hot-toast'
import { VehicleForm } from '@/components/forms/vehicle-form'
import { type CreateVehicleData } from '@/types'
import { ReceptionGuard } from '@/components/role-guard'

export default function NuevoVehiculoPage() {
  const router = useRouter()
  const queryClient = useQueryClient()

  const createVehicleMutation = useMutation({
    mutationFn: async (data: CreateVehicleData) => {
      console.log('Enviando datos:', data)
      try {
        const res = await server.api.vehicles.post(data)

        if(res.error) {
          toast.error('Error al crear el vehículo: ' + res.error.value.message)
          return;
        }
        console.log('Vehículo creado:', res.data)
        return res.data
      } catch (error) {
        console.error('Error en mutationFn:', error)
        throw error
      }
    },
    onSuccess: (data) => {
      console.log('onSuccess data:', data)
      if (data?.success) {
        toast.success('Vehículo creado exitosamente')
        queryClient.invalidateQueries({ queryKey: ['vehicles'] })
        router.push(`/vehiculos/${data.data.id}`)
      } else {
        console.error('Error en respuesta:', data)
        toast.error('Error al crear el vehículo')
      }
    },
    onError: (error) => {
      console.error('onError:', error)
      toast.error('Error al crear el vehículo: ' + (error as any)?.message || 'Error desconocido')
    }
  })

  const handleSubmit = (data: CreateVehicleData) => {
    createVehicleMutation.mutate(data)
  }

  const handleCancel = () => {
    router.push('/vehiculos')
  }

  return (
    <ReceptionGuard>
      <div className="p-6">
        <div className="max-w-4xl mx-auto">
          <DashboardHeader
            title="Nuevo Vehículo"
            description="Registra un nuevo vehículo en el sistema"
            backUrl="/vehiculos"
          />

          <VehicleForm
            onSubmit={handleSubmit}
            onCancel={handleCancel}
            isLoading={createVehicleMutation.isPending}
          />
        </div>
      </div>
    </ReceptionGuard>
  )
}
