"use client"

import { useRole } from '@/hooks/use-role'
import { UserRole } from '@/types'
import { DashboardStats } from '@/components/dashboard-stats'
import { RecentActivity } from '@/components/recent-activity'
import { QuickActions } from '@/components/quick-actions'
import { VehiclesInWorkshop } from '@/components/vehicles-in-workshop'
import { Button } from '@/components/ui/button'
import { Plus, Users, Package } from 'lucide-react'
import Link from 'next/link'

const roleDisplayNames = {
  [UserRole.ADMIN]: 'Administrador',
  [UserRole.RECEPCIONISTA]: 'Recepcionista',
  [UserRole.TECNICO]: 'Técnico',
  [UserRole.JEFE_TALLER]: 'Jefe de Taller',
  [UserRole.COMPRAS]: 'Compras',
  [UserRole.FACTURACION]: 'Facturación'
}

export default function DashboardPage() {
  const { user, currentRole } = useRole()

  if (!user) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">
            ¡Bienvenido, {user.name}!
          </h1>
          <p className="text-muted-foreground mt-1">
            {roleDisplayNames[currentRole]} - Sistema de Gestión de Taller
          </p>
        </div>

        {/* Quick Action Buttons */}
        <div className="flex gap-2">
          <Button asChild>
            <Link href="/recepcion/nuevo">
              <Plus className="w-4 h-4 mr-2" />
              Recibir Vehículo
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link href="/clientes/nuevo">
              <Users className="w-4 h-4 mr-2" />
              Nuevo Cliente
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link href="/vehiculos/nuevo">
              <Package className="w-4 h-4 mr-2" />
              Agregar Refacción
            </Link>
          </Button>
        </div>
      </div>

      {/* Dashboard Stats */}
      <DashboardStats />

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Vehicles in Workshop */}
        <div className="lg:col-span-2">
          <VehiclesInWorkshop />
        </div>

        {/* Recent Activity */}
        <div className="lg:col-span-1">
          <RecentActivity />
        </div>
      </div>

      {/* Secondary Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Quick Actions */}
        <div className="lg:col-span-3">
          <QuickActions />
        </div>
      </div>
    </div>
  )
}
