import { Elysia } from 'elysia'
import { auth } from '@/lib/auth'
import { HttpException } from '@/lib/exceptions/HttpExceptions';
import { Session, User } from '@prisma/client';

export const authMiddleware = (app: Elysia) =>
  app.derive(async (c) => {
    const session = await auth.api.getSession({ headers: c.request.headers }) as { user: User; session: Session } | null;

    if (!session || !session.user) {
      throw HttpException.Unauthorized();
    }

    return session;
  })
  // #F9D03B

// Middleware para verificar roles específicos
export const requireRole = (allowedRoles: string[]) => (app: Elysia) =>
  app
    .use(authMiddleware)
    .derive(({ user }) => {
      if (!allowedRoles.includes(user.role || '')) {
        throw HttpException.Forbidden('Insufficient permissions')
      }
      return { user }
    })

// Middleware para verificar que el usuario sea admin
export const requireAdmin = (app: Elysia) =>
  app
    .use(authMiddleware)
    .derive(({ user }) => {
      if (!['ADMIN', 'JEFE_TALLER'].includes(user.role || '')) {
        throw HttpException.Forbidden('Admin permissions required')
      }
      return { user }
    })
