import db from "@/config/db";
import { betterAuth } from "better-auth";
import { prismaAdapter } from "better-auth/adapters/prisma";
import { admin } from "better-auth/plugins";

export const auth = betterAuth({
    database: prismaAdapter(db, {
        provider: 'postgresql',
    }),
    secret: process.env.SECRET,
    session: {
        cookieCache: {
            enabled: false,
        }
    },
    pages: {
        signIn: '/login',
        error: '/auth-error'
    },
    databaseHooks: {
        user: {
            create: {
                before: async (data) => {
                    // Validar que el usuario tenga una invitación pendiente
                    const invitation = await db.invitation.findFirst({
                        where: {
                            email: data.email,
                            status: 'PENDING',
                            expiresAt: {
                                gt: new Date()
                            }
                        }
                    });

                    if (!invitation) {
                        throw new Error('No tienes una invitación válida para acceder al sistema. Contacta al administrador.');
                    }

                    // Si tiene invitación válida, asignar el rol de la invitación
                    data.role = invitation.role;
                    data.isActive = true;

                    // Validar que los talleres de la invitación existan y estén activos
                    console.log('Invitation workshopIds:', invitation.workshopIds);

                    if (!invitation.workshopIds || invitation.workshopIds.length === 0) {
                        throw new Error('La invitación no tiene talleres asignados. Contacta al administrador.');
                    }

                    const validWorkshops = await db.workshop.findMany({
                        where: {
                            id: { in: invitation.workshopIds },
                            isActive: true
                        }
                    });

                    console.log('Valid workshops found:', validWorkshops);

                    if (validWorkshops.length === 0) {
                        throw new Error('No hay talleres válidos asignados a esta invitación. Contacta al administrador.');
                    }

                    // Asignar el primer taller válido como taller actual
                    const firstValidWorkshopId = validWorkshops[0].id;
                    data.currentWorkshopId = firstValidWorkshopId;
                    console.log('Valid workshops:', validWorkshops.map(w => ({ id: w.id, name: w.name })));
                    console.log('Assigned currentWorkshopId:', firstValidWorkshopId);
                    console.log('Final data object:', { ...data });
                },
                after: async (user) => {
                    console.log('After hook - User created:', { id: user.id, email: user.email, currentWorkshopId: user.currentWorkshopId });

                    // Buscar la invitación para obtener los talleres asignados
                    const invitation = await db.invitation.findFirst({
                        where: {
                            email: user.email,
                            status: 'PENDING'
                        }
                    });

                    console.log('Found invitation in after hook:', invitation);

                    if (invitation && invitation.workshopIds) {
                        // Crear registros UserWorkshop para cada taller asignado
                        const userWorkshopData = invitation.workshopIds.map(workshopId => ({
                            userId: user.id,
                            workshopId: workshopId,
                            role: invitation.role,
                            isActive: true,
                            assignedAt: new Date(),
                            assignedBy: invitation.sentById
                        }));

                        console.log('Creating UserWorkshop records:', userWorkshopData);

                        await db.userWorkshop.createMany({
                            data: userWorkshopData
                        });

                        // Asegurar que el currentWorkshopId esté asignado
                        if (!user.currentWorkshopId && invitation.workshopIds.length > 0) {
                            console.log('Updating user currentWorkshopId to:', invitation.workshopIds[0]);
                            await db.user.update({
                                where: { id: user.id },
                                data: { currentWorkshopId: invitation.workshopIds[0] }
                            });
                        }
                    }

                    // Marcar la invitación como aceptada después de crear el usuario
                    await db.invitation.updateMany({
                        where: {
                            email: user.email,
                            status: 'PENDING'
                        },
                        data: {
                            status: 'ACCEPTED'
                        }
                    });
                }
            }
        }
    },
    user: {
        additionalFields: {
            currentWorkshopId: {
                type: "string",
                required: false,
            },
            role: {
                type: "string",
                required: true,
            },
            isActive: {
                type: "boolean",
                required: true,
            },
        }
    },
    emailAndPassword: {
        enabled: true,
        minPasswordLength: 8,
		maxPasswordLength: 128,
    },
    emailVerification: {
        sendVerificationEmail: async ({ user, token, url }) => {
            // Send verification email
            console.log('Send verification email', user, token, url);
        },
        expiresIn: 1000 * 60 * 60 * 24 * 7, // 7 days
    },
    socialProviders: {
        google: {
            enabled: true,
            clientId: process.env.GOOGLE_CLIENT_ID!,
            clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
        },
    },
    plugins: [
        admin({
            defaultRole: "RECEPCIONISTA",
            adminRoles: ["ADMIN", "JEFE_TALLER"],
            adminUserIds: [], // Se pueden agregar IDs específicos si es necesario
        })
    ]
});