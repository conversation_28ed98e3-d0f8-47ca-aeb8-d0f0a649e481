'use client'

import { useState, useEffect, useCallback } from 'react'
import { InvitationsTable } from './invitations-table'
import { InvitationsPagination } from './invitations-pagination'
import { server } from '@/app/api/server'
import toast from 'react-hot-toast'

interface Invitation {
  id: string
  email: string
  role: string
  status: 'PENDING' | 'ACCEPTED' | 'EXPIRED' | 'CANCELLED'
  expiresAt: string
  createdAt: string
  sentBy: {
    name: string
    email: string
  }
}

interface InvitationsTableWrapperProps {
  searchParams: {
    page?: string
    status?: string
    role?: string
    search?: string
  }
}

export function InvitationsTableWrapper({ searchParams }: InvitationsTableWrapperProps) {
  const [invitations, setInvitations] = useState<Invitation[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalCount: 0,
    limit: 10,
    hasNextPage: false,
    hasPreviousPage: false
  })

  const fetchInvitations = useCallback(async () => {
    setIsLoading(true)
    try {
      const params = new URLSearchParams()
      params.set('page', searchParams.page || '1')
      params.set('limit', '10')

      if (searchParams.status) params.set('status', searchParams.status)
      if (searchParams.role) params.set('role', searchParams.role)
      if (searchParams.search) params.set('search', searchParams.search)

      const response = await server.api.invitations.get({
        query: Object.fromEntries(params.entries())
      })

      if (response.data?.success && response.data.data) {
        // Convertir las fechas de Date a string para que coincidan con el tipo
        const formattedInvitations = response.data.data.invitations.map((inv: any) => ({
          ...inv,
          expiresAt: inv.expiresAt instanceof Date ? inv.expiresAt.toISOString() : inv.expiresAt,
          createdAt: inv.createdAt instanceof Date ? inv.createdAt.toISOString() : inv.createdAt
        }))
        setInvitations(formattedInvitations)
        setPagination(response.data.data.pagination)
      }
    } catch (error) {
      toast.error('Error al cargar las invitaciones')
    } finally {
      setIsLoading(false)
    }
  }, [searchParams])

  useEffect(() => {
    fetchInvitations()
  }, [searchParams])

  return (
    <div className="space-y-4">
      <InvitationsTable 
        invitations={invitations}
        isLoading={isLoading}
        onRefresh={fetchInvitations}
      />
      
      {!isLoading && invitations.length > 0 && (
        <InvitationsPagination
          currentPage={pagination.currentPage}
          totalPages={pagination.totalPages}
          totalCount={pagination.totalCount}
          itemsPerPage={pagination.limit}
        />
      )}
    </div>
  )
}
