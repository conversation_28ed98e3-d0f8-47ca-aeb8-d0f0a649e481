import { HttpStatus } from "./HttpStatus";

export class HttpException extends Error {
  public readonly statusCode: number;
  public readonly message: string;

  constructor(message: string, statusCode: number) {
    super(message);
    this.statusCode = statusCode;
    this.message = message;

    // Mantener el nombre de la clase correcta (importante para debugging)
    Object.setPrototypeOf(this, new.target.prototype);
  }

  // Métodos estáticos para lanzar excepciones comunes
  static BadRequest(message = 'Bad Request') {
    return new HttpException(message, HttpStatus.BAD_REQUEST);
  }

  static Unauthorized(message = 'Unauthorized') {
    return new HttpException(message, HttpStatus.UNAUTHORIZED);
  }

  static Forbidden(message = 'Forbidden') {
    return new HttpException(message, HttpStatus.FORBIDDEN);
  }

  static NotFound(message = 'Not Found') {
    return new HttpException(message, HttpStatus.NOT_FOUND);
  }

  static Conflict(message = 'Conflict') {
    return new HttpException(message, HttpStatus.CONFLICT);
  }

  static Gone(message = 'Gone') {
    return new HttpException(message, HttpStatus.GONE);
  }

  static InternalServerError(message = 'Internal Server Error') {
    return new HttpException(message, HttpStatus.INTERNAL_SERVER_ERROR);
  }

  static NotImplemented(message = 'Not Implemented') {
    return new HttpException(message, HttpStatus.NOT_IMPLEMENTED);
  }

  static BadGateway(message = 'Bad Gateway') {
    return new HttpException(message, HttpStatus.BAD_GATEWAY);
  }

  static ServiceUnavailable(message = 'Service Unavailable') {
    return new HttpException(message, HttpStatus.SERVICE_UNAVAILABLE);
  }

  static GatewayTimeout(message = 'Gateway Timeout') {
    return new HttpException(message, HttpStatus.GATEWAY_TIMEOUT);
  }
}