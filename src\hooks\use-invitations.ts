'use client'

import { useQuery } from '@tanstack/react-query'
import { server } from '@/app/api/server'
import { USER_INVITATIONS } from '@/constants/queries-key'

interface UseInvitationsParams {
  page?: number
  limit?: number
  status?: string
  role?: string
  search?: string
}

interface Invitation {
  id: string
  email: string
  role: string
  status: 'PENDING' | 'ACCEPTED' | 'EXPIRED' | 'CANCELLED'
  expiresAt: string
  createdAt: string
  workshopIds: string[]
  sentBy: {
    name: string
    email: string
  }
}

interface InvitationsResponse {
  invitations: Invitation[]
  pagination: {
    currentPage: number
    totalPages: number
    totalCount: number
    limit: number
    hasNextPage: boolean
    hasPreviousPage: boolean
  }
}

export function useInvitations(params: UseInvitationsParams = {}) {
  const {
    page = 1,
    limit = 10,
    status,
    role,
    search
  } = params

  return useQuery({
    queryKey: [USER_INVITATIONS, { page, limit, status, role, search }],
    queryFn: async (): Promise<InvitationsResponse> => {
      const queryParams = new URLSearchParams()
      queryParams.set('page', page.toString())
      queryParams.set('limit', limit.toString())
      
      if (status && status !== 'ALL') queryParams.set('status', status)
      if (role && role !== 'ALL') queryParams.set('role', role)
      if (search) queryParams.set('search', search)

      const response = await server.api.invitations.get({ 
        query: Object.fromEntries(queryParams.entries()) 
      })
      
      if (!response.data?.success) {
        throw new Error(response.data?.error || 'Error al cargar invitaciones')
      }

      // Convertir fechas si es necesario
      const formattedInvitations = response.data.data.invitations.map((inv: any) => ({
        ...inv,
        expiresAt: inv.expiresAt instanceof Date ? inv.expiresAt.toISOString() : inv.expiresAt,
        createdAt: inv.createdAt instanceof Date ? inv.createdAt.toISOString() : inv.createdAt
      }))

      return {
        invitations: formattedInvitations,
        pagination: response.data.data.pagination
      }
    },
    staleTime: 30000, // 30 seconds
    refetchOnWindowFocus: false
  })
}
