import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"

// Status configurations with exact colors from the example project
export const statusConfigs = {
  // Reception/Service Status
  'RECEIVED': { 
    color: 'bg-blue-100 text-blue-800 border-blue-200', 
    label: 'Recibido' 
  },
  'IN_DIAGNOSTIC': { 
    color: 'bg-yellow-100 text-yellow-800 border-yellow-200', 
    label: 'En Diagnóstico' 
  },
  'WAITING_APPROVAL': { 
    color: 'bg-orange-100 text-orange-800 border-orange-200', 
    label: 'Esperando Aprobación' 
  },
  'WAITING_PARTS': { 
    color: 'bg-purple-100 text-purple-800 border-purple-200', 
    label: 'Esperando Refacciones' 
  },
  'IN_REPAIR': { 
    color: 'bg-indigo-100 text-indigo-800 border-indigo-200', 
    label: 'En Reparación' 
  },
  'COMPLETED': { 
    color: 'bg-green-100 text-green-800 border-green-200', 
    label: 'Terminado' 
  },
  'DELIVERED': { 
    color: 'bg-gray-100 text-gray-800 border-gray-200', 
    label: 'Entregado' 
  },

  // Client Type Status
  'FLEET': { 
    color: 'bg-blue-100 text-blue-800 border-blue-200', 
    label: 'Flotillero' 
  },
  'INDIVIDUAL': { 
    color: 'bg-gray-100 text-gray-800 border-gray-200', 
    label: 'Individual' 
  },

  // Activity Status
  'success': { 
    color: 'bg-green-100 text-green-800 border-green-200', 
    label: 'Completado' 
  },
  'warning': { 
    color: 'bg-yellow-100 text-yellow-800 border-yellow-200', 
    label: 'Pendiente' 
  },
  'error': { 
    color: 'bg-red-100 text-red-800 border-red-200', 
    label: 'Error' 
  },
  'info': { 
    color: 'bg-blue-100 text-blue-800 border-blue-200', 
    label: 'Información' 
  },

  // Default
  'default': { 
    color: 'bg-gray-100 text-gray-800 border-gray-200', 
    label: 'Sin estado' 
  }
}

export type StatusType = keyof typeof statusConfigs

interface StatusBadgeProps {
  status: StatusType | string
  customLabel?: string
  className?: string
  size?: 'sm' | 'md' | 'lg'
}

export function StatusBadge({ 
  status, 
  customLabel, 
  className,
  size = 'md' 
}: StatusBadgeProps) {
  const config = statusConfigs[status as StatusType] || statusConfigs.default
  
  const sizeClasses = {
    sm: 'px-2 py-0.5 text-xs',
    md: 'px-2.5 py-0.5 text-xs',
    lg: 'px-3 py-1 text-sm'
  }

  return (
    <Badge 
      className={cn(
        config.color,
        sizeClasses[size],
        'font-semibold border',
        className
      )}
    >
      {customLabel || config.label}
    </Badge>
  )
}

// Helper function to get status badge for reception status
export function getReceptionStatusBadge(status: string) {
  return <StatusBadge status={status as StatusType} />
}

// Helper function to get client type badge
export function getClientTypeBadge(clientType: string) {
  return <StatusBadge status={clientType as StatusType} />
}

// Helper function to get activity status badge
export function getActivityStatusBadge(status: string, customLabel?: string) {
  return <StatusBadge status={status as StatusType} customLabel={customLabel} />
}
