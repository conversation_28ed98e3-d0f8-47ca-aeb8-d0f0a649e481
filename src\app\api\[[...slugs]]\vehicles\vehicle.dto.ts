import { t } from 'elysia'

export const CreateVehicleSchema = t.Object({
  brand: t.String({ minLength: 2, maxLength: 50 }),
  model: t.String({ minLength: 2, maxLength: 50 }),
  year: t.Number({ minimum: 1900, maximum: new Date().getFullYear() + 1 }),
  plates: t.String({ minLength: 6, maxLength: 10 }),
  vin: t.Optional(t.String({ minLength: 10, maxLength: 20 })),
  color: t.Optional(t.String({ maxLength: 30 })),
  clientId: t.String()
})

export const UpdateVehicleSchema = t.Object({
  brand: t.Optional(t.String({ minLength: 2, maxLength: 50 })),
  model: t.Optional(t.String({ minLength: 2, maxLength: 50 })),
  year: t.Optional(t.Number({ minimum: 1900, maximum: new Date().getFullYear() + 1 })),
  plates: t.Optional(t.String({ minLength: 6, maxLength: 10 })),
  vin: t.Optional(t.String({ minLength: 10, maxLength: 20 })),
  color: t.Optional(t.String({ maxLength: 30 })),
  clientId: t.Optional(t.String())
})

export const VehicleResponseSchema = t.Object({
  id: t.String(),
  brand: t.String(),
  model: t.String(),
  year: t.Number(),
  plates: t.String(),
  vin: t.Optional(t.String()),
  color: t.Optional(t.String()),
  clientId: t.String(),
  createdAt: t.Date(),
  updatedAt: t.Date()
})

export type CreateVehicleInput = typeof CreateVehicleSchema.static
export type UpdateVehicleInput = typeof UpdateVehicleSchema.static
export type VehicleResponse = typeof VehicleResponseSchema.static
