import { PrismaClient, UserRole } from '@prisma/client'

const prisma = new PrismaClient()

async function assignUserToWorkshop() {
  console.log('🔧 Asignando usuario de Google al taller por defecto...')

  try {
    // Find the Google user - get the most recent user
    const googleUser = await prisma.user.findFirst({
      where: {
        email: '<EMAIL>'
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    if (!googleUser) {
      console.log('❌ Usuario de Google no encontrado')
      return
    }

    // Find the default workshop
    const defaultWorkshop = await prisma.workshop.findUnique({
      where: { id: 'default-workshop-ruedda' }
    })

    if (!defaultWorkshop) {
      console.log('❌ Taller por defecto no encontrado')
      return
    }

    // Update user to have admin role and assign to workshop
    await prisma.user.update({
      where: { id: googleUser.id },
      data: {
        role: UserRole.ADMIN, // Make them admin
        currentWorkshopId: defaultWorkshop.id
      }
    })

    // Create UserWorkshop relationship
    await prisma.userWorkshop.upsert({
      where: {
        userId_workshopId: {
          userId: googleUser.id,
          workshopId: defaultWorkshop.id
        }
      },
      update: {
        role: UserRole.ADMIN,
        isActive: true
      },
      create: {
        userId: googleUser.id,
        workshopId: defaultWorkshop.id,
        role: UserRole.ADMIN,
        isActive: true,
        assignedAt: new Date()
      }
    })

    console.log('✅ Usuario asignado exitosamente:')
    console.log(`   • Usuario: ${googleUser.name} (${googleUser.email})`)
    console.log(`   • Rol: ADMIN`)
    console.log(`   • Taller: ${defaultWorkshop.name}`)
    console.log(`   • Estado: Activo`)

  } catch (error) {
    console.error('❌ Error asignando usuario:', error)
  } finally {
    await prisma.$disconnect()
  }
}

assignUserToWorkshop()
