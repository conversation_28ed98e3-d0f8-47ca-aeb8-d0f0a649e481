import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient, UserRole } from '@prisma/client'
import { getServerSession } from '@/lib/getSession'

const prisma = new PrismaClient()

export async function GET(
  request: NextRequest,
  { params }: { params: { workshopId: string } }
) {
  try {
    // Verify user is authenticated and is admin
    const session = await getServerSession({ shouldRedirect: false })
    
    if (!session || session.user.role !== UserRole.ADMIN) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin access required.' },
        { status: 403 }
      )
    }

    const { workshopId } = params

    // Get workshop details
    const workshop = await prisma.workshop.findUnique({
      where: { id: workshopId },
      include: {
        _count: {
          select: {
            users: {
              where: { isActive: true }
            },
            clients: true,
            vehicles: true,
            receptions: true
          }
        }
      }
    })

    if (!workshop) {
      return NextResponse.json(
        { error: 'Workshop not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      workshop: {
        id: workshop.id,
        name: workshop.name,
        location: workshop.location,
        phone: workshop.phone,
        email: workshop.email,
        isActive: workshop.isActive,
        createdAt: workshop.createdAt,
        userCount: workshop._count.users,
        clientCount: workshop._count.clients,
        vehicleCount: workshop._count.vehicles,
        receptionCount: workshop._count.receptions
      }
    })

  } catch (error) {
    console.error('Error fetching workshop:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
