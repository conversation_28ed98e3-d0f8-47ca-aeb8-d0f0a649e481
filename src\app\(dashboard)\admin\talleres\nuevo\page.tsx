"use client"

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useQueryClient } from '@tanstack/react-query'
import { useIsAdmin } from '@/hooks/use-role'
import { server } from '@/app/api/server'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { DashboardHeader } from '@/components/ui/dashboard-header'
import { 
  Building2, 
  MapPin, 
  Phone, 
  Mail,
  Save,
  Shield,
  Loader2
} from 'lucide-react'
import Link from 'next/link'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'

const workshopSchema = z.object({
  name: z.string().min(2, 'El nombre debe tener al menos 2 caracteres'),
  location: z.string().min(5, 'La ubicación debe tener al menos 5 caracteres'),
  phone: z.string().optional(),
  email: z.string().email('Email inválido').optional().or(z.literal('')),
  isActive: z.boolean().default(true),
})

type WorkshopFormData = z.infer<typeof workshopSchema>

export default function NewWorkshopPage() {
  const router = useRouter()
  const queryClient = useQueryClient()
  const isAdmin = useIsAdmin()
  const [isSubmitting, setIsSubmitting] = useState(false)

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors }
  } = useForm<WorkshopFormData>({
    resolver: zodResolver(workshopSchema),
    defaultValues: {
      isActive: true
    }
  })

  const isActive = watch('isActive')

  // Redirect if not admin
  if (!isAdmin) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5 text-destructive" />
              Acceso Denegado
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-4">
              No tienes permisos para crear talleres.
            </p>
            <Button asChild>
              <Link href="/admin">
                Volver al Panel de Admin
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  const onSubmit = async (data: WorkshopFormData) => {
    setIsSubmitting(true)

    try {
      const response = await server.api.workshops.post(data)

      if (!response.data?.success) {
        throw new Error(response.data?.error || 'Error creating workshop')
      }

      // Show success message
      console.log('Workshop created successfully:', response.data.data)

      // Invalidate queries to refresh data
      await queryClient.invalidateQueries({ queryKey: ['admin-workshops'] })
      await queryClient.invalidateQueries({ queryKey: ['user-workshops-switcher'] })

      // Redirect to workshops list
      router.push('/admin/talleres')
    } catch (error) {
      console.error('Error creating workshop:', error)
      // You could add a toast notification here
      alert('Error al crear el taller: ' + (error instanceof Error ? error.message : 'Error desconocido'))
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="p-6">
      <div className="max-w-4xl mx-auto">
        <DashboardHeader
          title="Crear Nuevo Taller"
          description="Configura un nuevo taller en el sistema"
          showBackButton={true}
          backUrl="/admin/talleres"
        />

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building2 className="h-5 w-5" />
                Información Básica
              </CardTitle>
              <CardDescription>
                Datos principales del taller
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Nombre del Taller *</Label>
                  <Input
                    id="name"
                    placeholder="Ej: Taller Central"
                    {...register('name')}
                    className={errors.name ? 'border-destructive' : ''}
                  />
                  {errors.name && (
                    <p className="text-sm text-destructive">{errors.name.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email">Email de Contacto</Label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                    <Input
                      id="email"
                      type="email"
                      placeholder="<EMAIL>"
                      className="pl-10"
                      {...register('email')}
                    />
                  </div>
                  {errors.email && (
                    <p className="text-sm text-destructive">{errors.email.message}</p>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="location">Ubicación *</Label>
                <div className="relative">
                  <MapPin className="absolute left-3 top-3 text-muted-foreground h-4 w-4" />
                  <Textarea
                    id="location"
                    placeholder="Dirección completa del taller..."
                    className="pl-10 min-h-[80px]"
                    {...register('location')}
                  />
                </div>
                {errors.location && (
                  <p className="text-sm text-destructive">{errors.location.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="phone">Teléfono</Label>
                <div className="relative">
                  <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                  <Input
                    id="phone"
                    type="tel"
                    placeholder="+52 55 1234 5678"
                    className="pl-10"
                    {...register('phone')}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Configuration */}
          <Card>
            <CardHeader>
              <CardTitle>Configuración</CardTitle>
              <CardDescription>
                Configuración inicial del taller
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="isActive">Estado del Taller</Label>
                  <p className="text-sm text-muted-foreground">
                    {isActive ? 'El taller estará activo y operativo' : 'El taller estará inactivo'}
                  </p>
                </div>
                <Switch
                  id="isActive"
                  checked={isActive}
                  onCheckedChange={(checked) => setValue('isActive', checked)}
                />
              </div>
            </CardContent>
          </Card>

          {/* Actions */}
          <div className="flex items-center justify-end gap-4">
            <Button type="button" variant="outline" asChild>
              <Link href="/admin/talleres">
                Cancelar
              </Link>
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Creando...
                </>
              ) : (
                <>
                  <Save className="w-4 h-4 mr-2" />
                  Crear Taller
                </>
              )}
            </Button>
          </div>
        </form>

        {/* Preview */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>Vista Previa</CardTitle>
            <CardDescription>
              Así se verá el taller una vez creado
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="border rounded-lg p-4">
              <div className="flex items-start gap-3">
                <Building2 className="h-8 w-8 text-primary" />
                <div className="flex-1">
                  <h3 className="font-semibold text-lg">
                    {watch('name') || 'Nombre del Taller'}
                  </h3>
                  <div className="space-y-1 mt-2">
                    <div className="flex items-start gap-2">
                      <MapPin className="h-4 w-4 text-muted-foreground mt-0.5" />
                      <span className="text-sm text-muted-foreground">
                        {watch('location') || 'Ubicación del taller'}
                      </span>
                    </div>
                    {watch('phone') && (
                      <div className="flex items-center gap-2">
                        <Phone className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm text-muted-foreground">{watch('phone')}</span>
                      </div>
                    )}
                    {watch('email') && (
                      <div className="flex items-center gap-2">
                        <Mail className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm text-muted-foreground">{watch('email')}</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
