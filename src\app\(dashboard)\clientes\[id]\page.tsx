'use client'

import { useParams } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Users,
  ArrowLeft,
  Edit,
  Phone,
  Mail,
  Building,
  User,
  Car,
  FileText,
  Calendar,
  MapPin,
  CreditCard
} from 'lucide-react'
import Link from 'next/link'
import { useQuery } from '@tanstack/react-query'
import { server } from '@/app/api/server'

export default function ClientDetailPage() {
  const params = useParams()
  const clientId = params.id as string

  const { data: client, isLoading } = useQuery({
    queryKey: ['client', clientId],
    queryFn: async () => {
      const response = await server.api.clients({ id: clientId }).get()
      // return response.data
      if (response.error) {
        throw new Error(response.error.value.message)
      }
      return response.data.data;
    }
  })
  console.log('client', client)

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-6xl mx-auto">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-gray-200 rounded w-1/3"></div>
            <div className="h-64 bg-gray-200 rounded"></div>
            <div className="h-48 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    )
  }

  if (!client) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-6xl mx-auto">
          <div className="text-center py-12">
            <Users className="h-16 w-16 mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Cliente no encontrado
            </h3>
            <p className="text-gray-500 mb-6">
              El cliente que buscas no existe o ha sido eliminado
            </p>
            <Button asChild>
              <Link href="/clientes">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Volver a Clientes
              </Link>
            </Button>
          </div>
        </div>
      </div>
    )
  }

  const getClientTypeBadge = (type: string) => {
    return type === 'FLEET' ? (
      <Badge variant="secondary" className="bg-blue-100 text-blue-800">
        <Building className="w-3 h-3 mr-1" />
        Flotillero
      </Badge>
    ) : (
      <Badge variant="outline">
        <User className="w-3 h-3 mr-1" />
        Individual
      </Badge>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-4">
            <Button variant="outline" size="sm" asChild>
              <Link href="/clientes">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Volver
              </Link>
            </Button>
          </div>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Users className="h-8 w-8" />
              <div>
                <h1 className="text-3xl font-bold text-gray-900">
                  {client.name}
                </h1>
                <div className="flex items-center gap-3 mt-2">
                  {getClientTypeBadge(client.clientType)}
                  {client.businessName && (
                    <span className="text-gray-600">{client.businessName}</span>
                  )}
                </div>
              </div>
            </div>
            
            <div className="flex gap-2">
              <Button asChild>
                <Link href={`/clientes/${client.id}/editar`}>
                  <Edit className="w-4 h-4 mr-2" />
                  Editar
                </Link>
              </Button>
              <Button asChild variant="outline">
                <Link href={`/vehiculos/nuevo?clientId=${client.id}`}>
                  <Car className="w-4 h-4 mr-2" />
                  Agregar Vehículo
                </Link>
              </Button>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Información Principal */}
          <div className="lg:col-span-2 space-y-6">
            {/* Datos de Contacto */}
            <Card>
              <CardHeader>
                <CardTitle>Información de Contacto</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center gap-3">
                    <Phone className="w-5 h-5 text-gray-400" />
                    <div>
                      <div className="text-sm text-gray-500">Teléfono</div>
                      <div className="font-medium">{client.phone}</div>
                    </div>
                  </div>

                  {client.email && (
                    <div className="flex items-center gap-3">
                      <Mail className="w-5 h-5 text-gray-400" />
                      <div>
                        <div className="text-sm text-gray-500">Email</div>
                        <div className="font-medium">{client.email}</div>
                      </div>
                    </div>
                  )}

                  {client.address && (
                    <div className="flex items-center gap-3 md:col-span-2">
                      <MapPin className="w-5 h-5 text-gray-400" />
                      <div>
                        <div className="text-sm text-gray-500">Dirección</div>
                        <div className="font-medium">{client.address}</div>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Datos Fiscales */}
            {(client.businessName || client.taxId) && (
              <Card>
                <CardHeader>
                  <CardTitle>Información Fiscal</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {client.businessName && (
                      <div className="flex items-center gap-3">
                        <Building className="w-5 h-5 text-gray-400" />
                        <div>
                          <div className="text-sm text-gray-500">Razón Social</div>
                          <div className="font-medium">{client.businessName}</div>
                        </div>
                      </div>
                    )}

                    {client.taxId && (
                      <div className="flex items-center gap-3">
                        <FileText className="w-5 h-5 text-gray-400" />
                        <div>
                          <div className="text-sm text-gray-500">RFC</div>
                          <div className="font-medium">{client.taxId}</div>
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Configuración de Flotilla */}
            {client.clientType === 'FLEET' && (client.paymentTerms || client.billingPeriod) && (
              <Card>
                <CardHeader>
                  <CardTitle>Configuración de Flotilla</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {client.paymentTerms && (
                      <div className="flex items-center gap-3">
                        <CreditCard className="w-5 h-5 text-gray-400" />
                        <div>
                          <div className="text-sm text-gray-500">Condiciones de Pago</div>
                          <div className="font-medium">{client.paymentTerms.replace('_', ' ')}</div>
                        </div>
                      </div>
                    )}

                    {client.billingPeriod && (
                      <div className="flex items-center gap-3">
                        <Calendar className="w-5 h-5 text-gray-400" />
                        <div>
                          <div className="text-sm text-gray-500">Período de Facturación</div>
                          <div className="font-medium">{client.billingPeriod}</div>
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Vehículos */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>Vehículos Registrados</CardTitle>
                  <Button asChild size="sm">
                    <Link href={`/vehiculos/nuevo?clientId=${client.id}`}>
                      <Car className="w-4 h-4 mr-2" />
                      Agregar Vehículo
                    </Link>
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                {client.vehicles && client.vehicles.length > 0 ? (
                  <div className="space-y-3">
                    {client.vehicles.map((vehicle: any) => (
                      <div key={vehicle.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center gap-3">
                          <Car className="w-5 h-5 text-gray-400" />
                          <div>
                            <div className="font-medium">
                              {vehicle.brand} {vehicle.model} {vehicle.year}
                            </div>
                            <div className="text-sm text-gray-500">
                              Placas: {vehicle.plates}
                              {vehicle.color && ` • Color: ${vehicle.color}`}
                            </div>
                          </div>
                        </div>
                        <Button asChild size="sm" variant="outline">
                          <Link href={`/vehiculos/${vehicle.id}`}>
                            Ver Detalle
                          </Link>
                        </Button>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <Car className="h-12 w-12 mx-auto mb-3 text-gray-300" />
                    <p>No hay vehículos registrados</p>
                    <Button asChild size="sm" className="mt-3">
                      <Link href={`/vehiculos/nuevo?clientId=${client.id}`}>
                        Registrar Primer Vehículo
                      </Link>
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Estadísticas */}
            <Card>
              <CardHeader>
                <CardTitle>Estadísticas</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">Vehículos</span>
                    <span className="font-semibold">{client.vehicles?.length || 0}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">Recepciones</span>
                    <span className="font-semibold">{client.receptions?.length || 0}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">Cliente desde</span>
                    <span className="font-semibold">
                      {new Date(client.createdAt).toLocaleDateString('es-MX')}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Acciones Rápidas */}
            <Card>
              <CardHeader>
                <CardTitle>Acciones Rápidas</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Button asChild className="w-full" variant="outline">
                    <Link href={`/recepcion/nuevo?clientId=${client.id}`}>
                      <FileText className="w-4 h-4 mr-2" />
                      Nueva Recepción
                    </Link>
                  </Button>
                  <Button asChild className="w-full" variant="outline">
                    <Link href={`/vehiculos/nuevo?clientId=${client.id}`}>
                      <Car className="w-4 h-4 mr-2" />
                      Agregar Vehículo
                    </Link>
                  </Button>
                  <Button asChild className="w-full" variant="outline">
                    <Link href={`/clientes/${client.id}/editar`}>
                      <Edit className="w-4 h-4 mr-2" />
                      Editar Cliente
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
