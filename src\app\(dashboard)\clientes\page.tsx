'use client'

import { useState, useMemo } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { StatusBadge, getClientTypeBadge } from '@/components/status-badge'
import { Users, UserPlus, Search, Building, User, Phone, Mail, Car, MoreHorizontal } from 'lucide-react'
import Link from 'next/link'
import { DashboardHeader } from '@/components/ui/dashboard-header'
import { useQuery } from '@tanstack/react-query'
import { server } from '@/app/api/server'
import { useWorkshopQuery } from '@/hooks/use-workshop-query'
import {
  useReactTable,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  createColumnHelper,
  flexRender,
} from '@tanstack/react-table'

const columnHelper = createColumnHelper<any>()

export default function ClientsPage() {
  const [search, setSearch] = useState('')
  const [typeFilter, setTypeFilter] = useState<'INDIVIDUAL' | 'FLEET' | ''>('')
  const { createQueryKey } = useWorkshopQuery()

  const { data: clientsData, isLoading } = useQuery({
    queryKey: createQueryKey(['clients'], { search, clientType: typeFilter }),
    queryFn: async () => {
      const response = await server.api.clients.get({
        query: {
          search: search || undefined,
          clientType: typeFilter || undefined
        }
      })
      return response.data
    }
  })

  const clients = clientsData?.success ? clientsData.data : []

  const columns = useMemo(() => [
    columnHelper.accessor('name', {
      header: 'Cliente',
      cell: (info) => (
        <div>
          <div className="font-medium">{info.getValue()}</div>
          {info.row.original.businessName && (
            <div className="text-sm text-gray-500">{info.row.original.businessName}</div>
          )}
        </div>
      ),
    }),
    columnHelper.accessor('clientType', {
      header: 'Tipo',
      cell: (info) => getClientTypeBadge(info.getValue()),
    }),
    columnHelper.accessor('phone', {
      header: 'Contacto',
      cell: (info) => (
        <div>
          <div className="flex items-center gap-1 text-sm">
            <Phone className="w-3 h-3" />
            {info.getValue()}
          </div>
          {info.row.original.email && (
            <div className="flex items-center gap-1 text-sm text-gray-500">
              <Mail className="w-3 h-3" />
              {info.row.original.email}
            </div>
          )}
        </div>
      ),
    }),
    columnHelper.accessor('_count', {
      header: 'Vehículos',
      cell: (info) => (
        <div className="flex items-center gap-1">
          <Car className="w-4 h-4 text-gray-400" />
          <span>{info.getValue()?.vehiculos || 0}</span>
        </div>
      ),
    }),
    columnHelper.display({
      id: 'actions',
      header: 'Acciones',
      cell: (info) => (
        <div className="flex gap-2">
          <Button asChild size="sm" variant="outline">
            <Link href={`/clientes/${info.row.original.id}`}>
              Ver
            </Link>
          </Button>
          <Button asChild size="sm" variant="ghost">
            <Link href={`/clientes/${info.row.original.id}/editar`}>
              Editar
            </Link>
          </Button>
        </div>
      ),
    }),
  ], [])

  const table = useReactTable({
    data: clients,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    state: {
      globalFilter: search,
    },
    onGlobalFilterChange: setSearch,
  })

  // Using the centralized getClientTypeBadge function

  return (
    <div className="p-6">
      <div className="max-w-7xl mx-auto">
        <div className="flex items-center justify-between mb-8">
          <DashboardHeader
            title="Gestión de Clientes"
            description="Administra clientes individuales y flotilleros"
            showBackButton={false}
          />
          <Button asChild>
            <Link href="/clientes/nuevo">
              <UserPlus className="w-4 h-4 mr-2" />
              Nuevo Cliente
            </Link>
          </Button>
        </div>

        {/* Filtros */}
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder="Buscar por nombre, teléfono, email o razón social..."
                    value={search}
                    onChange={(e) => setSearch(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <Button
                  variant={typeFilter === '' ? 'default' : 'outline'}
                  onClick={() => setTypeFilter('')}
                  size="sm"
                >
                  Todos
                </Button>
                <Button
                  variant={typeFilter === 'INDIVIDUAL' ? 'default' : 'outline'}
                  onClick={() => setTypeFilter('INDIVIDUAL')}
                  size="sm"
                >
                  <User className="w-4 h-4 mr-1" />
                  Individual
                </Button>
                <Button
                  variant={typeFilter === 'FLEET' ? 'default' : 'outline'}
                  onClick={() => setTypeFilter('FLEET')}
                  size="sm"
                >
                  <Building className="w-4 h-4 mr-1" />
                  Flotillero
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Lista de Clientes */}
        {isLoading ? (
          <Card>
            <CardContent className="pt-6">
              <div className="animate-pulse space-y-4">
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="flex items-center space-x-4">
                    <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/6"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/6"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/6"></div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        ) : clients.length === 0 ? (
          <Card>
            <CardContent className="pt-6">
              <div className="text-center py-12">
                <Users className="h-16 w-16 mx-auto text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  No hay clientes registrados
                </h3>
                <p className="text-gray-500 mb-6">
                  {search || typeFilter
                    ? 'No se encontraron clientes con los filtros aplicados'
                    : 'Comienza registrando tu primer cliente'
                  }
                </p>
                <Button asChild>
                  <Link href="/clientes/nuevo">
                    <UserPlus className="w-4 h-4 mr-2" />
                    Registrar Cliente
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        ) : (
          <Card>
            <CardContent className="pt-6">
              <div className="rounded-md border">
                <table className="w-full">
                  <thead>
                    {table.getHeaderGroups().map((headerGroup) => (
                      <tr key={headerGroup.id} className="border-b bg-gray-50">
                        {headerGroup.headers.map((header) => (
                          <th
                            key={header.id}
                            className="px-4 py-3 text-left text-sm font-medium text-gray-900"
                          >
                            {header.isPlaceholder
                              ? null
                              : flexRender(
                                  header.column.columnDef.header,
                                  header.getContext()
                                )}
                          </th>
                        ))}
                      </tr>
                    ))}
                  </thead>
                  <tbody>
                    {table.getRowModel().rows.map((row) => (
                      <tr key={row.id} className="border-b hover:bg-gray-50">
                        {row.getVisibleCells().map((cell) => (
                          <td key={cell.id} className="px-4 py-3">
                            {flexRender(cell.column.columnDef.cell, cell.getContext())}
                          </td>
                        ))}
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Paginación */}
              <div className="flex items-center justify-between space-x-2 py-4">
                <div className="text-sm text-gray-500">
                  Mostrando {table.getRowModel().rows.length} de {clients.length} clientes
                </div>
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => table.previousPage()}
                    disabled={!table.getCanPreviousPage()}
                  >
                    Anterior
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => table.nextPage()}
                    disabled={!table.getCanNextPage()}
                  >
                    Siguiente
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Resumen */}
        {clients.length > 0 && (
          <Card className="mt-8">
            <CardHeader>
              <CardTitle className="text-lg">Resumen</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-blue-600">
                    {clients.length}
                  </div>
                  <div className="text-sm text-gray-500">Total</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-green-600">
                    {clients.filter((c: any) => c.tipoCliente === 'INDIVIDUAL').length}
                  </div>
                  <div className="text-sm text-gray-500">Individuales</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-purple-600">
                    {clients.filter((c: any) => c.tipoCliente === 'FLOTILLERO').length}
                  </div>
                  <div className="text-sm text-gray-500">Flotilleros</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-orange-600">
                    {clients.reduce((acc: number, c: any) => acc + (c._count?.vehiculos || 0), 0)}
                  </div>
                  <div className="text-sm text-gray-500">Vehículos</div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
