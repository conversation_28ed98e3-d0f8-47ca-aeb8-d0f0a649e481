import { Elysia, t } from 'elysia'
import { InvitationService } from '../invitations/invitation.service'

export const publicController = new Elysia({ prefix: '/public' })
  .get('/invitation/:token', async ({ params }) => {
    try {
      const invitation = await InvitationService.getInvitationByToken(params.token);
      
      return {
        success: true,
        data: invitation
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Error desconocido"
      };
    }
  }, {
    params: t.Object({
      token: t.String()
    })
  })
