'use client'

import { ColumnDef } from '@tanstack/react-table'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { MoreHorizontal, Calendar, User } from 'lucide-react'
import { format } from 'date-fns'
import { es } from 'date-fns/locale'
import { server } from '@/app/api/server'
import { useQueryClient } from '@tanstack/react-query'
import { USER_INVITATIONS } from '@/constants/queries-key'
import toast from 'react-hot-toast'

interface Invitation {
  id: string
  email: string
  role: string
  status: 'PENDING' | 'ACCEPTED' | 'EXPIRED' | 'CANCELLED'
  expiresAt: string
  createdAt: string
  workshopIds: string[]
  sentBy: {
    name: string
    email: string
  }
}

const STATUS_COLORS = {
  PENDING: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  ACCEPTED: 'bg-green-100 text-green-800 border-green-200',
  EXPIRED: 'bg-red-100 text-red-800 border-red-200',
  CANCELLED: 'bg-gray-100 text-gray-800 border-gray-200'
}

const STATUS_LABELS = {
  PENDING: 'Pendiente',
  ACCEPTED: 'Aceptada',
  EXPIRED: 'Expirada',
  CANCELLED: 'Cancelada'
}

const ROLE_LABELS = {
  ADMIN: 'Administrador',
  RECEPCIONISTA: 'Recepcionista',
  TECNICO: 'Técnico',
  JEFE_TALLER: 'Jefe de Taller',
  COMPRAS: 'Compras',
  FACTURACION: 'Facturación'
}

function ActionsCell({ invitation }: { invitation: Invitation }) {
  const queryClient = useQueryClient()

  const handleCancelInvitation = async (id: string) => {
    try {
      const response = await server.api.invitations({ id }).cancel.post()
      
      if (response.data?.success) {
        toast.success('Invitación cancelada')
        queryClient.invalidateQueries({ queryKey: [USER_INVITATIONS] })
      } else {
        toast.error(response.data?.error || 'Error al cancelar la invitación')
      }
    } catch (error) {
      toast.error('Error al cancelar la invitación')
    }
  }

  const handleResendInvitation = async (id: string) => {
    try {
      const response = await server.api.invitations({ id }).resend.post()
      
      if (response.data?.success) {
        toast.success('Invitación reenviada')
        queryClient.invalidateQueries({ queryKey: [USER_INVITATIONS] })
      } else {
        toast.error(response.data?.error || 'Error al reenviar la invitación')
      }
    } catch (error) {
      toast.error('Error al reenviar la invitación')
    }
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        {invitation.status === 'PENDING' && (
          <>
            <DropdownMenuItem onClick={() => handleResendInvitation(invitation.id)}>
              Reenviar
            </DropdownMenuItem>
            <DropdownMenuItem 
              onClick={() => handleCancelInvitation(invitation.id)}
              className="text-red-600"
            >
              Cancelar
            </DropdownMenuItem>
          </>
        )}
        {invitation.status === 'ACCEPTED' && (
          <DropdownMenuItem disabled>
            Invitación aceptada
          </DropdownMenuItem>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

export const invitationsColumns: ColumnDef<Invitation>[] = [
  {
    accessorKey: 'email',
    header: 'Email',
    cell: ({ row }) => (
      <div className="font-medium">{row.getValue('email')}</div>
    )
  },
  {
    accessorKey: 'role',
    header: 'Rol',
    cell: ({ row }) => (
      <Badge variant="outline">
        {ROLE_LABELS[row.getValue('role') as keyof typeof ROLE_LABELS]}
      </Badge>
    )
  },
  {
    accessorKey: 'status',
    header: 'Estado',
    cell: ({ row }) => {
      const status = row.getValue('status') as keyof typeof STATUS_COLORS
      return (
        <Badge className={STATUS_COLORS[status]}>
          {STATUS_LABELS[status]}
        </Badge>
      )
    }
  },
  {
    accessorKey: 'sentBy',
    header: 'Enviado por',
    cell: ({ row }) => {
      const sentBy = row.getValue('sentBy') as { name: string; email: string }
      return (
        <div className="flex items-center gap-2">
          <User className="h-4 w-4" />
          <span className="text-sm">{sentBy.name}</span>
        </div>
      )
    }
  },
  {
    accessorKey: 'createdAt',
    header: 'Fecha de creación',
    cell: ({ row }) => (
      <div className="flex items-center gap-2">
        <Calendar className="h-4 w-4" />
        <span className="text-sm">
          {format(new Date(row.getValue('createdAt')), 'dd/MM/yyyy HH:mm', { locale: es })}
        </span>
      </div>
    )
  },
  {
    accessorKey: 'expiresAt',
    header: 'Expira',
    cell: ({ row }) => (
      <span className="text-sm">
        {format(new Date(row.getValue('expiresAt')), 'dd/MM/yyyy', { locale: es })}
      </span>
    )
  },
  {
    id: 'actions',
    cell: ({ row }) => <ActionsCell invitation={row.original} />
  }
]
