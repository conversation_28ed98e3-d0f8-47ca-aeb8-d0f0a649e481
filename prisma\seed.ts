import { PrismaClient, UserRole } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Starting database seed...')

  // Create default admin user
  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      id: 'admin-user-id',
      name: '<PERSON><PERSON><PERSON><PERSON>',
      email: '<EMAIL>',
      emailVerified: true,
      role: UserRole.ADMIN,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  })

  console.log('✅ Admin user created:', adminUser.email)

  // Create default workshop "Ruedda"
  const defaultWorkshop = await prisma.workshop.upsert({
    where: { id: 'default-workshop-ruedda' },
    update: {},
    create: {
      id: 'default-workshop-ruedda',
      name: 'Ruedda',
      location: 'Av. Principal 123, Centro, Ciudad de México, CDMX 06000',
      phone: '+52 55 1234 5678',
      email: '<EMAIL>',
      isActive: true,
      settings: {
        businessHours: {
          monday: { open: '08:00', close: '18:00' },
          tuesday: { open: '08:00', close: '18:00' },
          wednesday: { open: '08:00', close: '18:00' },
          thursday: { open: '08:00', close: '18:00' },
          friday: { open: '08:00', close: '18:00' },
          saturday: { open: '08:00', close: '14:00' },
          sunday: { closed: true }
        },
        currency: 'MXN',
        timezone: 'America/Mexico_City',
        features: {
          photoEvidence: true,
          diagnostics: true,
          inventory: true,
          billing: true
        }
      },
      createdById: adminUser.id,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  })

  console.log('✅ Default workshop created:', defaultWorkshop.name)

  // Assign admin user to the default workshop
  await prisma.userWorkshop.upsert({
    where: {
      userId_workshopId: {
        userId: adminUser.id,
        workshopId: defaultWorkshop.id,
      },
    },
    update: {},
    create: {
      userId: adminUser.id,
      workshopId: defaultWorkshop.id,
      role: UserRole.ADMIN,
      isActive: true,
      assignedAt: new Date(),
    },
  })

  // Set the default workshop as current for admin
  await prisma.user.update({
    where: { id: adminUser.id },
    data: {
      currentWorkshopId: defaultWorkshop.id,
    },
  })

  console.log('✅ Admin user assigned to default workshop')

  // Create a sample receptionist user
  const receptionistUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      id: 'receptionist-user-id',
      name: 'María Recepcionista',
      email: '<EMAIL>',
      emailVerified: true,
      role: UserRole.RECEPCIONISTA,
      isActive: true,
      currentWorkshopId: defaultWorkshop.id,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  })

  // Assign receptionist to workshop
  await prisma.userWorkshop.upsert({
    where: {
      userId_workshopId: {
        userId: receptionistUser.id,
        workshopId: defaultWorkshop.id,
      },
    },
    update: {},
    create: {
      userId: receptionistUser.id,
      workshopId: defaultWorkshop.id,
      role: UserRole.RECEPCIONISTA,
      isActive: true,
      assignedAt: new Date(),
    },
  })

  console.log('✅ Receptionist user created and assigned')

  // Create a sample technician user
  const technicianUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      id: 'technician-user-id',
      name: 'Carlos Técnico',
      email: '<EMAIL>',
      emailVerified: true,
      role: UserRole.TECNICO,
      isActive: true,
      currentWorkshopId: defaultWorkshop.id,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  })

  // Assign technician to workshop
  await prisma.userWorkshop.upsert({
    where: {
      userId_workshopId: {
        userId: technicianUser.id,
        workshopId: defaultWorkshop.id,
      },
    },
    update: {},
    create: {
      userId: technicianUser.id,
      workshopId: defaultWorkshop.id,
      role: UserRole.TECNICO,
      isActive: true,
      assignedAt: new Date(),
    },
  })

  console.log('✅ Technician user created and assigned')

  console.log('🎉 Database seed completed successfully!')
  console.log('')
  console.log('📋 Summary:')
  console.log(`   • Workshop: ${defaultWorkshop.name}`)
  console.log(`   • Admin: ${adminUser.email}`)
  console.log(`   • Receptionist: ${receptionistUser.email}`)
  console.log(`   • Technician: ${technicianUser.email}`)
  console.log('')
  console.log('🔑 You can now login with any of these users (no password required in development)')
}

main()
  .catch((e) => {
    console.error('❌ Error during seed:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
